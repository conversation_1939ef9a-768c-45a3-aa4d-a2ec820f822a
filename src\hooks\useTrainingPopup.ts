import { useState, useEffect } from 'react';

export const useTrainingPopup = () => {
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  useEffect(() => {
    // Check if popup was already shown in this session
    const popupShown = sessionStorage.getItem('trainingPopupShown');
    
    if (!popupShown) {
      // Show popup after 3 seconds
      const timer = setTimeout(() => {
        setIsPopupOpen(true);
        sessionStorage.setItem('trainingPopupShown', 'true');
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, []);

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  return {
    isPopupOpen,
    closePopup
  };
};
