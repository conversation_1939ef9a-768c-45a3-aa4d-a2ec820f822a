<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Newsletter Subscription Confirmation - <PERSON><PERSON> Wolf</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
        }
        
        .header {
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #000;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .header-subtitle {
            color: #000;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .welcome-title {
            font-size: 24px;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .message {
            font-size: 16px;
            color: #cccccc;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .subscriber-info {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        
        .subscriber-email {
            font-size: 18px;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 10px;
        }
        
        .subscription-date {
            font-size: 14px;
            color: #999;
        }
        
        .features {
            margin: 30px 0;
        }
        
        .features-title {
            font-size: 20px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid #00ff88;
        }
        
        .feature-icon {
            width: 20px;
            height: 20px;
            background: #00ff88;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #000;
            font-size: 12px;
        }
        
        .cta-section {
            text-align: center;
            margin: 30px 0;
            padding: 25px;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 204, 106, 0.1) 100%);
            border-radius: 10px;
            border: 1px solid rgba(0, 255, 136, 0.3);
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            color: #000;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            transition: transform 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.4);
        }
        
        .footer {
            background: #1a1a1a;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #333;
        }
        
        .footer-text {
            color: #999;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .social-link {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: #333;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #00ff88;
            text-decoration: none;
            transition: background 0.3s ease;
        }
        
        .social-link:hover {
            background: #00ff88;
            color: #000;
        }
        
        .unsubscribe {
            color: #666;
            font-size: 12px;
            text-decoration: none;
        }
        
        .unsubscribe:hover {
            color: #00ff88;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header, .content, .footer {
                padding: 20px;
            }
            
            .logo {
                font-size: 24px;
            }
            
            .welcome-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🐺 CYBER WOLF</div>
            <div class="header-subtitle">Cybersecurity Excellence & Innovation</div>
        </div>
        
        <div class="content">
            <h1 class="welcome-title">Welcome to Our Newsletter!</h1>
            
            <p class="message">
                Thank you for subscribing to Cyber Wolf's newsletter. You're now part of our cybersecurity community and will receive the latest insights, tutorials, and industry updates.
            </p>
            
            <div class="subscriber-info">
                <div class="subscriber-email">{{user_email}}</div>
                <div class="subscription-date">Subscribed on {{subscription_date}}</div>
            </div>
            
            <div class="features">
                <h2 class="features-title">What You'll Receive:</h2>
                <ul class="feature-list">
                    <li class="feature-item">
                        <span class="feature-icon">📚</span>
                        <span>Latest cybersecurity articles and tutorials</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🔒</span>
                        <span>Advanced penetration testing techniques</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🛡️</span>
                        <span>Security frameworks and compliance guides</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🎯</span>
                        <span>Exclusive CTF challenges and solutions</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">📊</span>
                        <span>Industry insights and threat intelligence</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🚀</span>
                        <span>Early access to new tools and resources</span>
                    </li>
                </ul>
            </div>
            
            <div class="cta-section">
                <p style="margin-bottom: 20px; color: #cccccc;">
                    Start exploring our cybersecurity resources and join our community!
                </p>
                <a href="https://cyberwolf-career-guidance.web.app" class="cta-button">
                    Explore Cyber Wolf Platform
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Follow us on social media for daily cybersecurity tips and updates
            </p>
            
            <div class="social-links">
                <a href="#" class="social-link" title="LinkedIn">💼</a>
                <a href="#" class="social-link" title="Instagram">📷</a>
                <a href="#" class="social-link" title="WhatsApp">💬</a>
                <a href="#" class="social-link" title="GitHub">🔗</a>
            </div>
            
            <p class="footer-text">
                © 2024 Cyber Wolf. All rights reserved.<br>
                <a href="#" class="unsubscribe">Unsubscribe from this newsletter</a>
            </p>
        </div>
    </div>
</body>
</html>
