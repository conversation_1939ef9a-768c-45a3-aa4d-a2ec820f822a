import { useRef, useEffect, useCallback } from 'react';

// Terminal typing sound hook
export const useTerminalSounds = () => {
  const audioContextRef = useRef<AudioContext | null>(null);
  const lastKeypressTime = useRef<number>(0);

  // Initialize audio context
  useEffect(() => {
    const initAudio = () => {
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      }
    };

    const handleFirstInteraction = () => {
      initAudio();
      document.removeEventListener('keydown', handleFirstInteraction);
      document.removeEventListener('click', handleFirstInteraction);
    };

    document.addEventListener('keydown', handleFirstInteraction, { once: true });
    document.addEventListener('click', handleFirstInteraction, { once: true });

    return () => {
      document.removeEventListener('keydown', handleFirstInteraction);
      document.removeEventListener('click', handleFirstInteraction);
    };
  }, []);

  // Create typing sound
  const createTypingSound = useCallback((frequency: number, duration: number, volume: number = 0.1) => {
    if (!audioContextRef.current) return;

    const context = audioContextRef.current;
    const oscillator = context.createOscillator();
    const gainNode = context.createGain();
    const filterNode = context.createBiquadFilter();

    oscillator.connect(filterNode);
    filterNode.connect(gainNode);
    gainNode.connect(context.destination);

    // Configure filter for more realistic typing sound
    filterNode.type = 'highpass';
    filterNode.frequency.setValueAtTime(200, context.currentTime);

    oscillator.frequency.setValueAtTime(frequency, context.currentTime);
    oscillator.type = 'square';

    gainNode.gain.setValueAtTime(0, context.currentTime);
    gainNode.gain.linearRampToValueAtTime(volume, context.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, context.currentTime + duration);

    oscillator.start(context.currentTime);
    oscillator.stop(context.currentTime + duration);
  }, []);

  // Different typing sounds
  const playKeystroke = useCallback((key: string) => {
    const now = Date.now();
    
    // Throttle rapid keystrokes to prevent audio overload
    if (now - lastKeypressTime.current < 50) return;
    lastKeypressTime.current = now;

    let frequency = 800;
    let duration = 0.08;
    let volume = 0.08;

    // Different sounds for different key types
    switch (key) {
      case 'Enter':
        frequency = 600;
        duration = 0.15;
        volume = 0.12;
        break;
      case 'Backspace':
        frequency = 400;
        duration = 0.1;
        volume = 0.1;
        break;
      case 'Tab':
        frequency = 1000;
        duration = 0.12;
        volume = 0.1;
        break;
      case ' ':
        frequency = 500;
        duration = 0.06;
        volume = 0.06;
        break;
      case 'ArrowUp':
      case 'ArrowDown':
      case 'ArrowLeft':
      case 'ArrowRight':
        frequency = 900;
        duration = 0.08;
        volume = 0.08;
        break;
      default:
        // Regular character keys
        frequency = 800 + Math.random() * 200; // Add slight variation
        duration = 0.08;
        volume = 0.08;
        break;
    }

    createTypingSound(frequency, duration, volume);
  }, [createTypingSound]);

  const playCommandExecute = useCallback(() => {
    createTypingSound(1200, 0.2, 0.15);
  }, [createTypingSound]);

  const playSystemBeep = useCallback(() => {
    createTypingSound(800, 0.1, 0.1);
  }, [createTypingSound]);

  const playErrorSound = useCallback(() => {
    createTypingSound(300, 0.3, 0.12);
  }, [createTypingSound]);

  const playSuccessSound = useCallback(() => {
    // Play a pleasant success chord
    setTimeout(() => createTypingSound(523, 0.15, 0.08), 0);   // C
    setTimeout(() => createTypingSound(659, 0.15, 0.08), 50);  // E
    setTimeout(() => createTypingSound(784, 0.15, 0.08), 100); // G
  }, [createTypingSound]);

  return {
    playKeystroke,
    playCommandExecute,
    playSystemBeep,
    playErrorSound,
    playSuccessSound
  };
};
