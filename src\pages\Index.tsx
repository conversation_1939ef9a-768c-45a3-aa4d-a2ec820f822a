import React, { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Monitor, Calendar, Clock, Shield, Award, Users, ArrowRight, ExternalLink, BookOpen, Target, Code, Zap, MessageCircle, UserCheck } from "lucide-react";
import cyberwolfLogo from "@/assets/cyberwolf-logo.png";
import speaker1 from "@/assets/speaker1.png";
import speaker2 from "@/assets/speaker2.png";
import TrainingPopup from "@/components/TrainingPopup";
import UpcomingEvents from "@/components/UpcomingEvents";
import LinuxCommands from "@/components/LinuxCommands";
import { useTrainingPopup } from "@/hooks/useTrainingPopup";

const Index = () => {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Training popup functionality
  const { isPopupOpen, closePopup } = useTrainingPopup();

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT">
      {/* Floating Social Media Icons */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-50 flex flex-col space-y-4">
        {/* Instagram Icon */}
        <a
          href="https://www.instagram.com/p/DL7imlFvUHk/?igsh=MXJjbG9scng0dnZrZA=="
          target="_blank"
          rel="noopener noreferrer"
          className="group relative"
        >
          <div className="w-14 h-14 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110">
            <svg
              className="w-7 h-7 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
            </svg>
          </div>
          <div className="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
            Follow on Instagram
          </div>
        </a>

        {/* LinkedIn Icon */}
        <a
          href="https://www.linkedin.com/company/cyberwolf-team/"
          target="_blank"
          rel="noopener noreferrer"
          className="group relative"
        >
          <div className="w-14 h-14 bg-gradient-to-br from-blue-600 to-blue-800 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110">
            <svg
              className="w-7 h-7 text-white"
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
            </svg>
          </div>
          <div className="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
            Connect on LinkedIn
          </div>
        </a>
      </div>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-6 md:px-12">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl top-1/4 left-1/4 animate-pulse"></div>
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/5 rounded-full blur-3xl bottom-1/4 right-1/4 animate-pulse"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center">
            {/* Logo */}
            <div className="flex justify-center mb-8">
              <img 
                src={cyberwolfLogo} 
                alt="Cyber Wolf Logo" 
                className="w-32 h-32 object-contain animate-fade-in"
              />
            </div>
            
            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-in">
              CYBER WOLF 
              <span className="block text-psyco-green-DEFAULT">FREE TRAINING COURSE</span>
            </h1>
            
            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 mb-8 animate-fade-in">
              Master Ethical Hacking & Cybersecurity with Industry Experts
            </p>
            
            {/* Course Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
              <div className="glassmorphism p-6 rounded-xl animate-fade-in">
                <Monitor className="text-psyco-green-DEFAULT mx-auto mb-3" size={32} />
                <h3 className="text-white font-bold mb-2">Online Mode</h3>
                <p className="text-gray-400 text-sm">Join from anywhere</p>
              </div>
              <div className="glassmorphism p-6 rounded-xl animate-fade-in">
                <Calendar className="text-psyco-green-DEFAULT mx-auto mb-3" size={32} />
                <h3 className="text-white font-bold mb-2">18th July 2025</h3>
                <p className="text-gray-400 text-sm">Friday</p>
              </div>
              <div className="glassmorphism p-6 rounded-xl animate-fade-in">
                <Clock className="text-psyco-green-DEFAULT mx-auto mb-3" size={32} />
                <h3 className="text-white font-bold mb-2">11:00 AM – 3:00 PM</h3>
                <p className="text-gray-400 text-sm">IST (4 Hours)</p>
              </div>
            </div>
            
            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in">
              <Link
                to="/registration"
                className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 flex items-center justify-center text-lg btn-glow"
              >
                Register Now - FREE
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="bg-transparent border-2 border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT hover:text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 flex items-center justify-center text-lg"
              >
                Contact Us
                <ExternalLink className="ml-2 h-5 w-5" />
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* What You'll Learn Section */}
      <section className="py-20 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">What You'll Master</h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Comprehensive hands-on training in web penetration testing and ethical hacking techniques
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Shield size={40} />,
                title: "Web Penetration Testing Fundamentals",
                description: "Learn the core principles and methodologies of ethical hacking"
              },
              {
                icon: <Target size={40} />,
                title: "OWASP Top 10 Vulnerabilities",
                description: "Deep dive into the most critical web application security risks"
              },
              {
                icon: <Code size={40} />,
                title: "Industry-Standard Tools",
                description: "Master Burp Suite, Nmap, Nikto, and other professional tools"
              },
              {
                icon: <Zap size={40} />,
                title: "Real-time Hacking Demos",
                description: "Watch live demonstrations of actual penetration testing scenarios"
              },
              {
                icon: <BookOpen size={40} />,
                title: "Ethical Hacking Techniques",
                description: "Learn responsible disclosure and ethical hacking practices"
              },
              {
                icon: <Award size={40} />,
                title: "Professional Certificate",
                description: "Receive a certificate of completion to boost your career"
              }
            ].map((feature, index) => (
              <div 
                key={index}
                className="glassmorphism p-8 rounded-xl text-center hover:scale-105 transition-transform duration-300 animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="text-psyco-green-DEFAULT mb-4 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-400">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Instructors Section */}
      <section className="py-20 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">Meet Your Instructors</h2>
            <p className="text-xl text-gray-400">
              Learn from experienced cybersecurity professionals at Cyber Wolf
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
            <div className="glassmorphism p-8 rounded-xl text-center animate-fade-in">
              <img 
                src={speaker1} 
                alt="Senior Security Researcher" 
                className="w-32 h-32 rounded-full object-cover mx-auto mb-6 border-4 border-psyco-green-DEFAULT"
              />
              <h3 className="text-2xl font-bold text-white mb-2">Tamilselvan S</h3>
              <p className="text-psyco-green-DEFAULT font-medium mb-4">Lead Security Researcher</p>
              <p className="text-gray-400">
                Specialized in web application security with 4+ years of experience in penetration testing and vulnerability assessment.
              </p>
            </div>
            
            <div className="glassmorphism p-8 rounded-xl text-center animate-fade-in">
              <img 
                src={speaker2} 
                alt="Penetration Testing Specialist" 
                className="w-32 h-32 rounded-full object-cover mx-auto mb-6 border-4 border-psyco-green-DEFAULT"
              />
              <h3 className="text-2xl font-bold text-white mb-2">Pugazhmani K</h3>
              <p className="text-psyco-green-DEFAULT font-medium mb-4">Security Research Team</p>
              <p className="text-gray-400">
                Expert in ethical hacking methodologies with extensive experience in security auditing and vulnerability research.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">Why Choose This Course?</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <Award size={48} />,
                title: "100% Free",
                description: "No hidden fees or charges"
              },
              {
                icon: <Users size={48} />,
                title: "Expert Instructors",
                description: "Learn from industry professionals"
              },
              {
                icon: <Shield size={48} />,
                title: "Hands-on Training",
                description: "Practical, real-world scenarios"
              },
              {
                icon: <BookOpen size={48} />,
                title: "Certificate Included",
                description: "Professional completion certificate"
              }
            ].map((benefit, index) => (
              <div 
                key={index}
                className="glassmorphism p-8 rounded-xl text-center animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="text-psyco-green-DEFAULT mb-4 flex justify-center">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{benefit.title}</h3>
                <p className="text-gray-400">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Upcoming Events Section */}
      <UpcomingEvents />

      
      {/* Contact & Registration CTA */}
      <section className="py-20 px-6 md:px-12 relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Ready to Start Your Cybersecurity Journey?</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Join thousands of students who have started their cybersecurity careers with our expert-led courses.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
            <Link
              to="/registration"
              className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-bold py-4 px-12 rounded-lg transition-all duration-300 flex items-center text-xl btn-glow"
            >
              Secure Your Spot Now
              <ArrowRight className="ml-3 h-6 w-6" />
            </Link>
          </div>
          
          {/* Contact Information */}
          <div className="glassmorphism p-8 rounded-xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-6">Contact Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <h4 className="text-psyco-green-DEFAULT font-semibold mb-2">Email</h4>
                <a 
                  href="mailto:<EMAIL>"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
              <div>
                <h4 className="text-psyco-green-DEFAULT font-semibold mb-2">Phone 1</h4>
                <a 
                  href="tel:+916374344424"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  +91 6374 344 424
                </a>
              </div>
              <div>
                <h4 className="text-psyco-green-DEFAULT font-semibold mb-2">Phone 2</h4>
                <a 
                  href="tel:+916379869678"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  +91 6379 869 678
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Training Popup */}
      <TrainingPopup isOpen={isPopupOpen} onClose={closePopup} />
    </div>
  );
};

export default Index;
