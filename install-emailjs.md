# EmailJS Installation Instructions

## Install EmailJS Package

Run the following command in your project directory to install EmailJS:

```bash
npm install @emailjs/browser
```

Or if you're using yarn:

```bash
yarn add @emailjs/browser
```

## Verify Installation

After installation, you should see `@emailjs/browser` in your `package.json` dependencies.

## Next Steps

1. Follow the setup guide in `emailjs-setup.md`
2. Configure your EmailJS account
3. Update the credentials in `src/pages/Registration.tsx`
4. Test the registration form

The EmailJS integration is now ready to use!
