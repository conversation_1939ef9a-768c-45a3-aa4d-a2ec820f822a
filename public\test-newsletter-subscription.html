<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Newsletter Subscription - Cy<PERSON> Wolf</title>
    <script src="https://cdn.emailjs.com/dist/email.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border-radius: 15px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #cccccc;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #cccccc;
            font-weight: 500;
        }
        
        input[type="email"] {
            width: 100%;
            padding: 12px 16px;
            background: #333;
            border: 1px solid #555;
            border-radius: 8px;
            color: #ffffff;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="email"]:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
        }
        
        .btn {
            width: 100%;
            padding: 12px 16px;
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            color: #000;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #000;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .message {
            margin-top: 20px;
            padding: 12px 16px;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
        }
        
        .success {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid #00ff88;
            color: #00ff88;
        }
        
        .error {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid #ff4444;
            color: #ff4444;
        }
        
        .features {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #333;
        }
        
        .features h3 {
            color: #00ff88;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .features ul {
            list-style: none;
            padding: 0;
        }
        
        .features li {
            padding: 8px 0;
            color: #cccccc;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .features li::before {
            content: "✓";
            color: #00ff88;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🐺 CYBER WOLF</div>
            <div class="subtitle">Newsletter Subscription Test</div>
        </div>
        
        <form id="subscriptionForm">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required placeholder="Enter your email address">
            </div>
            
            <button type="submit" class="btn" id="submitBtn">
                <span id="btnText">Subscribe to Newsletter</span>
            </button>
        </form>
        
        <div id="message" class="message" style="display: none;"></div>
        
        <div class="features">
            <h3>What You'll Receive:</h3>
            <ul>
                <li>Latest cybersecurity articles</li>
                <li>Penetration testing tutorials</li>
                <li>Security frameworks guides</li>
                <li>Exclusive CTF challenges</li>
                <li>Industry threat intelligence</li>
                <li>Early access to new tools</li>
            </ul>
        </div>
    </div>

    <script>
        // Initialize EmailJS
        emailjs.init('UReJUOuf7nicAZWRS');
        
        const form = document.getElementById('subscriptionForm');
        const submitBtn = document.getElementById('submitBtn');
        const btnText = document.getElementById('btnText');
        const messageDiv = document.getElementById('message');
        
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            
            if (!email || !email.includes('@')) {
                showMessage('Please enter a valid email address.', 'error');
                return;
            }
            
            // Show loading state
            submitBtn.disabled = true;
            btnText.innerHTML = '<div class="spinner"></div> Subscribing...';
            hideMessage();
            
            try {
                const templateParams = {
                    user_email: email,
                    subscription_date: new Date().toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    }),
                    to_email: email,
                    from_name: 'Cyber Wolf Team',
                    message: `Welcome to Cyber Wolf Newsletter! Thank you for subscribing with ${email}. You'll receive the latest cybersecurity insights, tutorials, and industry updates.`
                };
                
                await emailjs.send(
                    'service_05rrk3a',
                    'template_9p1ynh2',
                    templateParams
                );
                
                showMessage('Successfully subscribed! Check your email for confirmation.', 'success');
                form.reset();
                
            } catch (error) {
                console.error('EmailJS Error:', error);
                showMessage('Subscription failed. Please try again later.', 'error');
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                btnText.textContent = 'Subscribe to Newsletter';
            }
        });
        
        function showMessage(text, type) {
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                hideMessage();
            }, 5000);
        }
        
        function hideMessage() {
            messageDiv.style.display = 'none';
        }
    </script>
</body>
</html>
