
import React, { useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { ArrowLeft, Calendar, Clock, User, Tag, Share2 } from "lucide-react";
import { cn } from "@/lib/utils";

const getBlogPostById = (id: string) => {
  const blogPosts = {
    "security-research-framework": {
      title: "Advanced Security Research Framework: Comprehensive Reconnaissance Guide",
      date: "January 10, 2024",
      readTime: "18 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "/images/ChatGPT Image Jul 13, 2025, 02_47_45 PM.png",
      content: [
        {
          type: "paragraph",
          text: "Advanced security research requires a systematic approach to intelligence gathering and reconnaissance. This comprehensive framework, developed by <PERSON><PERSON>, provides security researchers with professional-grade tools and methodologies for conducting thorough passive reconnaissance operations."
        },
        {
          type: "heading",
          text: "Phase 1: Reconnaissance & Intelligence Gathering"
        },
        {
          type: "paragraph",
          text: "The foundation of any successful security assessment begins with comprehensive reconnaissance. This phase involves gathering as much information as possible about the target without directly interacting with their systems, maintaining stealth and avoiding detection."
        },
        {
          type: "heading",
          text: "Advanced Passive Information Gathering Framework"
        },
        {
          type: "paragraph",
          text: "The following Python framework demonstrates advanced passive reconnaissance techniques integrating multiple OSINT sources:"
        },
        {
          type: "code",
          language: "python",
          code: `#!/usr/bin/env python3
"""
Advanced Passive Reconnaissance Framework
Author: S. Tamilselvan
Purpose: Comprehensive target intelligence gathering with OSINT integration
"""

import requests
import dns.resolver
import whois
import shodan
import json
import subprocess
import re
import threading
from concurrent.futures import ThreadPoolExecutor
import time
import socket
from urllib.parse import urlparse, urljoin
import ssl
import hashlib

class AdvancedPassiveRecon:
    def __init__(self, target_domain):
        self.target = target_domain
        self.results = {
            'domain_info': {},
            'dns_records': {},
            'subdomains': [],
            'technology_stack': {},
            'social_media': {},
            'email_addresses': [],
            'leaked_credentials': [],
            'certificates': {},
            'whois_data': {},
            'shodan_data': {},
            'github_repos': [],
            'pastebin_leaks': []
        }

    def comprehensive_recon(self):
        """Execute comprehensive passive reconnaissance"""
        print(f"[*] Starting comprehensive reconnaissance for {self.target}")

        # Multi-threaded reconnaissance
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(self.dns_enumeration),
                executor.submit(self.subdomain_enumeration),
                executor.submit(self.technology_detection),
                executor.submit(self.whois_lookup),
                executor.submit(self.certificate_transparency),
                executor.submit(self.social_media_discovery),
                executor.submit(self.email_harvesting),
                executor.submit(self.github_reconnaissance),
                executor.submit(self.pastebin_search)
            ]

            for future in futures:
                try:
                    future.result(timeout=60)
                except Exception as e:
                    print(f"[!] Reconnaissance task failed: {e}")

        return self.results`
        },
        {
          type: "heading",
          text: "DNS Enumeration & Subdomain Discovery"
        },
        {
          type: "paragraph",
          text: "DNS enumeration is crucial for mapping the target's infrastructure. The framework implements comprehensive DNS record analysis and advanced subdomain discovery techniques:"
        },
        {
          type: "code",
          language: "python",
          code: `def dns_enumeration(self):
    """Comprehensive DNS enumeration"""
    print("[*] Performing DNS enumeration...")
    dns_records = ['A', 'AAAA', 'MX', 'NS', 'TXT', 'CNAME', 'SOA', 'PTR', 'SRV']

    for record_type in dns_records:
        try:
            answers = dns.resolver.resolve(self.target, record_type)
            self.results['dns_records'][record_type] = [str(answer) for answer in answers]
        except:
            self.results['dns_records'][record_type] = []

    # DNS Zone Transfer attempt
    try:
        ns_servers = self.results['dns_records'].get('NS', [])
        for ns in ns_servers:
            zone_transfer = self.attempt_zone_transfer(ns)
            if zone_transfer:
                self.results['dns_records']['zone_transfer'] = zone_transfer
    except:
        pass

def subdomain_enumeration(self):
    """Advanced subdomain discovery using multiple techniques"""
    print("[*] Enumerating subdomains...")
    subdomains = set()

    # Dictionary-based enumeration
    wordlist = [
        'www', 'mail', 'ftp', 'admin', 'test', 'dev', 'staging', 'api', 'app',
        'mobile', 'secure', 'vpn', 'remote', 'blog', 'shop', 'store', 'support',
        'help', 'docs', 'portal', 'dashboard', 'panel', 'cpanel', 'webmail',
        'mx', 'ns1', 'ns2', 'dns', 'email', 'smtp', 'pop', 'imap', 'exchange',
        'owa', 'autodiscover', 'lyncdiscover', 'sip', 'voip', 'pbx', 'citrix',
        'vpn', 'ssl', 'secure', 'login', 'auth', 'sso', 'ldap', 'ad', 'dc',
        'backup', 'archive', 'old', 'legacy', 'deprecated', 'beta', 'alpha',
        'demo', 'sandbox', 'lab', 'research', 'internal', 'intranet', 'extranet'
    ]

    def check_subdomain(sub):
        try:
            full_domain = f"{sub}.{self.target}"
            dns.resolver.resolve(full_domain, 'A')
            subdomains.add(full_domain)
            print(f"[+] Found subdomain: {full_domain}")
        except:
            pass

    with ThreadPoolExecutor(max_workers=50) as executor:
        executor.map(check_subdomain, wordlist)

    # Certificate Transparency logs
    ct_subdomains = self.certificate_transparency_subdomains()
    subdomains.update(ct_subdomains)

    # Search engine enumeration
    search_subdomains = self.search_engine_subdomains()
    subdomains.update(search_subdomains)

    self.results['subdomains'] = list(subdomains)`
        },
        {
          type: "heading",
          text: "Technology Stack Detection"
        },
        {
          type: "paragraph",
          text: "Understanding the target's technology stack is essential for identifying potential attack vectors. The framework performs comprehensive technology fingerprinting:"
        },
        {
          type: "code",
          language: "python",
          code: `def technology_detection(self):
    """Advanced technology stack detection"""
    print("[*] Detecting technology stack...")

    try:
        response = requests.get(f"http://{self.target}", timeout=10, allow_redirects=True)
        headers = response.headers
        content = response.text

        tech_stack = {
            'server': headers.get('Server', 'Unknown'),
            'x_powered_by': headers.get('X-Powered-By', 'Unknown'),
            'framework': self.detect_framework(content, headers),
            'cms': self.detect_cms(content, headers),
            'javascript_libraries': self.detect_js_libraries(content),
            'programming_language': self.detect_programming_language(content, headers),
            'database': self.detect_database_hints(content, headers),
            'cdn': self.detect_cdn(headers),
            'security_headers': self.analyze_security_headers(headers)
        }

        self.results['technology_stack'] = tech_stack

    except Exception as e:
        print(f"[!] Technology detection failed: {e}")

def detect_framework(self, content, headers):
    """Detect web framework"""
    frameworks = {
        'Django': [r'csrfmiddlewaretoken', r'django', r'__admin_media_prefix__'],
        'Laravel': [r'laravel_session', r'_token', r'laravel'],
        'Spring': [r'JSESSIONID', r'spring', r'struts'],
        'ASP.NET': [r'__VIEWSTATE', r'__EVENTVALIDATION', r'aspnet'],
        'Ruby on Rails': [r'authenticity_token', r'rails', r'_csrf_token'],
        'Express.js': [r'express', r'connect.sid'],
        'Flask': [r'flask', r'session'],
        'CodeIgniter': [r'ci_session', r'codeigniter'],
        'Symfony': [r'symfony', r'_sf2_attributes'],
        'Zend': [r'zend', r'ZENDPHPSESSID']
    }

    detected = []
    content_lower = content.lower()

    for framework, patterns in frameworks.items():
        for pattern in patterns:
            if re.search(pattern, content_lower, re.IGNORECASE):
                detected.append(framework)
                break

    return list(set(detected))`
        },
        {
          type: "heading",
          text: "OSINT Integration & Intelligence Gathering"
        },
        {
          type: "paragraph",
          text: "The framework integrates multiple OSINT sources for comprehensive intelligence gathering, including GitHub reconnaissance, email harvesting, and certificate transparency analysis:"
        },
        {
          type: "code",
          language: "python",
          code: `def github_reconnaissance(self):
    """Search for GitHub repositories and leaked information"""
    print("[*] Searching GitHub for leaked information...")

    search_queries = [
        f'"{self.target}"',
        f'"{self.target}" password',
        f'"{self.target}" api_key',
        f'"{self.target}" secret',
        f'"{self.target}" config',
        f'"{self.target}" database',
        f'"{self.target}" credentials'
    ]

    repos = []

    for query in search_queries:
        try:
            # GitHub search implementation would go here
            # repos.extend(github_search(query))
            pass
        except:
            pass

    self.results['github_repos'] = repos

def certificate_transparency(self):
    """Query Certificate Transparency logs"""
    print("[*] Querying Certificate Transparency logs...")

    try:
        # Query crt.sh
        url = f"https://crt.sh/?q=%.{self.target}&output=json"
        response = requests.get(url, timeout=30)

        if response.status_code == 200:
            certs = response.json()
            cert_info = []

            for cert in certs[:10]:  # Limit to first 10 certificates
                cert_info.append({
                    'id': cert.get('id'),
                    'name_value': cert.get('name_value'),
                    'not_before': cert.get('not_before'),
                    'not_after': cert.get('not_after'),
                    'issuer_name': cert.get('issuer_name')
                })

            self.results['certificates'] = cert_info
    except Exception as e:
        print(f"[!] Certificate transparency query failed: {e}")

def shodan_lookup(self, api_key):
    """Shodan intelligence gathering"""
    if not api_key:
        return

    print("[*] Performing Shodan lookup...")

    try:
        api = shodan.Shodan(api_key)

        # Search by domain
        results = api.search(f'hostname:{self.target}')

        shodan_data = {
            'total_results': results['total'],
            'hosts': []
        }

        for result in results['matches'][:5]:  # Limit to first 5 results
            host_info = {
                'ip': result['ip_str'],
                'port': result['port'],
                'protocol': result['transport'],
                'service': result.get('product', 'Unknown'),
                'version': result.get('version', 'Unknown'),
                'banner': result.get('data', '')[:200],  # Limit banner size
                'location': {
                    'country': result.get('location', {}).get('country_name'),
                    'city': result.get('location', {}).get('city')
                },
                'vulnerabilities': result.get('vulns', [])
            }
            shodan_data['hosts'].append(host_info)

        self.results['shodan_data'] = shodan_data

    except Exception as e:
        print(f"[!] Shodan lookup failed: {e}")`
        },
        {
          type: "heading",
          text: "Framework Usage & Implementation"
        },
        {
          type: "paragraph",
          text: "The complete framework can be implemented and executed as follows:"
        },
        {
          type: "code",
          language: "python",
          code: `# Usage example for the reconnaissance framework
if __name__ == "__main__":
    recon = AdvancedPassiveRecon("example.com")
    results = recon.comprehensive_recon()

    print("\\n" + "="*60)
    print("RECONNAISSANCE RESULTS")
    print("="*60)
    print(f"Subdomains found: {len(results['subdomains'])}")
    print(f"Email addresses: {len(results['email_addresses'])}")
    print(f"Technologies detected: {len(results['technology_stack'])}")

    # Save results
    with open('recon_results.json', 'w') as f:
        json.dump(results, f, indent=2)

    print("\\n[+] Results saved to recon_results.json")
    print("[+] Reconnaissance completed successfully")`
        },
        {
          type: "heading",
          text: "Best Practices & Ethical Considerations"
        },
        {
          type: "paragraph",
          text: "When implementing this reconnaissance framework, security researchers must adhere to ethical guidelines and legal requirements:"
        },
        {
          type: "list",
          items: [
            "Always obtain proper authorization before conducting reconnaissance",
            "Respect rate limits and avoid overwhelming target systems",
            "Use reconnaissance data responsibly and maintain confidentiality",
            "Follow responsible disclosure practices for any vulnerabilities discovered",
            "Comply with local laws and regulations regarding security testing",
            "Document all activities for audit and compliance purposes",
            "Implement proper data handling and storage procedures"
          ]
        },
        {
          type: "quote",
          text: "Effective security research requires not just technical skills, but also ethical responsibility and professional integrity. The goal is to improve security, not to cause harm.",
          author: "S.Tamilselvan, Security Researcher"
        },
        {
          type: "paragraph",
          text: "This advanced reconnaissance framework provides security researchers with the tools and methodologies needed for comprehensive passive intelligence gathering. By combining multiple OSINT sources and automation techniques, researchers can efficiently map target infrastructure and identify potential security issues while maintaining ethical standards and legal compliance."
        }
      ],
      relatedPosts: ["threat-intelligence-matrix", "advanced-osint-techniques", "web-penetration-testing-fundamentals"]
    },
    "threat-intelligence-matrix": {
      title: "Comprehensive Vulnerability Classification & Threat Intelligence Matrix",
      date: "January 8, 2024",
      readTime: "15 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://pub.mdpi-res.com/jcp/jcp-04-00040/article_deploy/html/images/jcp-04-00040-g001-550.jpg?1728295050",
      content: [
        {
          type: "paragraph",
          text: "Effective threat intelligence requires systematic classification and analysis of vulnerabilities. This comprehensive matrix, developed by S.Tamilselvan, provides security professionals with a structured approach to vulnerability assessment, risk prioritization, and threat analysis using industry-standard metrics and methodologies."
        },
        {
          type: "heading",
          text: "Vulnerability Classification Framework"
        },
        {
          type: "paragraph",
          text: "The threat intelligence matrix categorizes vulnerabilities across multiple dimensions to provide comprehensive risk assessment capabilities. Each vulnerability is evaluated using standardized metrics including CVSS scoring, exploitability assessment, business impact analysis, and detection complexity."
        },
        {
          type: "heading",
          text: "Comprehensive Attack Vector Analysis"
        },
        {
          type: "paragraph",
          text: "The following matrix provides detailed analysis of common attack vectors with quantitative risk metrics:"
        },
        {
          type: "code",
          language: "text",
          code: `COMPREHENSIVE VULNERABILITY CLASSIFICATION MATRIX
================================================================

Attack Vector           | CVSS | Exploit | Impact  | Detection | Prevalence | Remediation | Automation
                       | Score| Level   | Level   | Difficulty| Level      | Complexity  | Level
-----------------------|------|---------|---------|-----------|------------|-------------|------------
SQL Injection          | 9.8  | High    | Critical| Medium    | High       | Medium      | High
NoSQL Injection        | 9.5  | High    | Critical| Hard      | Medium     | High        | Medium
XSS (Stored)          | 8.8  | High    | High    | Easy      | Very High  | Low         | High
XSS (Reflected)       | 7.5  | High    | Medium  | Easy      | Very High  | Low         | High
XSS (DOM-based)       | 7.2  | Medium  | Medium  | Hard      | High       | Medium      | Medium
CSRF                  | 6.5  | Medium  | Medium  | Medium    | Medium     | Low         | High
SSRF                  | 8.6  | Medium  | High    | Hard      | Medium     | Medium      | Medium
XXE (XML External)    | 8.2  | Medium  | High    | Medium    | Low        | Medium      | Medium
Directory Traversal   | 7.5  | High    | Medium  | Easy      | High       | Low         | High
File Upload RCE       | 9.3  | High    | Critical| Easy      | Medium     | Medium      | Medium
Command Injection     | 9.8  | High    | Critical| Medium    | Medium     | Medium      | Medium
JWT Vulnerabilities   | 7.8  | Medium  | High    | Medium    | Medium     | Medium      | High
Insecure Deserial.    | 9.0  | Medium  | Critical| Hard      | Low        | High        | Low
LDAP Injection        | 8.1  | Medium  | High    | Medium    | Low        | Medium      | Medium
Template Injection    | 8.5  | Medium  | High    | Hard      | Low        | High        | Low
Race Conditions       | 6.8  | Hard    | Medium  | Very Hard | Low        | High        | Low
Business Logic Flaws  | 7.0  | Medium  | High    | Very Hard | Medium     | High        | Very Low
Authentication Bypass | 8.9  | Medium  | Critical| Medium    | Medium     | Medium      | Medium
Session Management    | 7.6  | Medium  | High    | Medium    | High       | Medium      | Medium
Privilege Escalation  | 8.4  | Medium  | High    | Hard      | Medium     | High        | Low`
        },
        {
          type: "heading",
          text: "Risk Scoring Methodology"
        },
        {
          type: "paragraph",
          text: "The matrix employs a multi-dimensional scoring system that considers various factors affecting vulnerability risk and exploitability:"
        },
        {
          type: "list",
          items: [
            "CVSS Score: Common Vulnerability Scoring System (0.0-10.0 scale)",
            "Exploitability Level: Ease of exploitation (Low/Medium/High/Critical)",
            "Business Impact: Potential damage to business operations",
            "Detection Difficulty: Complexity of identifying the vulnerability",
            "Prevalence Level: Frequency of occurrence in real-world applications",
            "Remediation Complexity: Effort required to fix the vulnerability",
            "Automation Level: Degree to which exploitation can be automated"
          ]
        },
        {
          type: "heading",
          text: "Critical Vulnerability Categories"
        },
        {
          type: "paragraph",
          text: "Based on the threat intelligence analysis, the following categories represent the highest-risk vulnerabilities requiring immediate attention:"
        },
        {
          type: "code",
          language: "python",
          code: `# Critical Vulnerability Analysis Framework
class ThreatIntelligenceMatrix:
    def __init__(self):
        self.critical_vulnerabilities = {
            'sql_injection': {
                'cvss_score': 9.8,
                'attack_vectors': [
                    'Union-based injection',
                    'Boolean-based blind injection',
                    'Time-based blind injection',
                    'Error-based injection',
                    'Out-of-band injection'
                ],
                'exploitation_techniques': [
                    'Database enumeration',
                    'Data exfiltration',
                    'Authentication bypass',
                    'Privilege escalation',
                    'Remote code execution'
                ],
                'detection_methods': [
                    'Static code analysis',
                    'Dynamic application testing',
                    'Web application firewalls',
                    'Database activity monitoring',
                    'Behavioral analysis'
                ],
                'mitigation_strategies': [
                    'Parameterized queries',
                    'Input validation',
                    'Least privilege principles',
                    'Database hardening',
                    'Regular security testing'
                ]
            },
            'command_injection': {
                'cvss_score': 9.8,
                'attack_vectors': [
                    'OS command injection',
                    'Code injection',
                    'Script injection',
                    'Expression injection'
                ],
                'exploitation_techniques': [
                    'System command execution',
                    'File system access',
                    'Network reconnaissance',
                    'Privilege escalation',
                    'Lateral movement'
                ],
                'detection_methods': [
                    'Input validation testing',
                    'Runtime application monitoring',
                    'System call monitoring',
                    'Anomaly detection',
                    'Code review'
                ],
                'mitigation_strategies': [
                    'Input sanitization',
                    'Command whitelisting',
                    'Sandboxing',
                    'Principle of least privilege',
                    'Runtime protection'
                ]
            }
        }

    def calculate_risk_score(self, vulnerability):
        """Calculate comprehensive risk score"""
        base_score = vulnerability['cvss_score']
        exploitability = self.get_exploitability_factor(vulnerability)
        prevalence = self.get_prevalence_factor(vulnerability)
        business_impact = self.get_business_impact_factor(vulnerability)

        # Weighted risk calculation
        risk_score = (base_score * 0.4) + (exploitability * 0.3) + (prevalence * 0.2) + (business_impact * 0.1)

        return min(risk_score, 10.0)  # Cap at 10.0

    def prioritize_vulnerabilities(self, vulnerabilities):
        """Prioritize vulnerabilities based on comprehensive risk analysis"""
        prioritized = []

        for vuln in vulnerabilities:
            risk_score = self.calculate_risk_score(vuln)
            priority_level = self.determine_priority_level(risk_score)

            prioritized.append({
                'vulnerability': vuln,
                'risk_score': risk_score,
                'priority': priority_level,
                'recommended_timeline': self.get_remediation_timeline(priority_level)
            })

        return sorted(prioritized, key=lambda x: x['risk_score'], reverse=True)`
        },
        {
          type: "heading",
          text: "Threat Intelligence Integration"
        },
        {
          type: "paragraph",
          text: "The matrix integrates real-time threat intelligence to provide dynamic risk assessment capabilities:"
        },
        {
          type: "code",
          language: "python",
          code: `class ThreatIntelligenceIntegration:
    def __init__(self):
        self.threat_feeds = [
            'MITRE ATT&CK Framework',
            'OWASP Top 10',
            'CVE Database',
            'NVD (National Vulnerability Database)',
            'SANS Top 25',
            'CWE (Common Weakness Enumeration)'
        ]

    def update_threat_landscape(self):
        """Update threat intelligence with latest data"""
        threat_data = {}

        # MITRE ATT&CK integration
        threat_data['attack_techniques'] = self.fetch_mitre_techniques()

        # CVE feed integration
        threat_data['recent_cves'] = self.fetch_recent_cves()

        # Exploit database integration
        threat_data['active_exploits'] = self.fetch_exploit_data()

        # Threat actor intelligence
        threat_data['threat_actors'] = self.fetch_threat_actor_data()

        return threat_data

    def correlate_vulnerabilities(self, vulnerabilities, threat_data):
        """Correlate vulnerabilities with active threats"""
        correlated_threats = []

        for vuln in vulnerabilities:
            # Check for active exploits
            active_exploits = self.check_active_exploits(vuln, threat_data)

            # Analyze threat actor interest
            actor_interest = self.analyze_threat_actor_interest(vuln, threat_data)

            # Calculate threat correlation score
            correlation_score = self.calculate_correlation_score(
                vuln, active_exploits, actor_interest
            )

            correlated_threats.append({
                'vulnerability': vuln,
                'active_exploits': active_exploits,
                'threat_actors': actor_interest,
                'correlation_score': correlation_score,
                'urgency_level': self.determine_urgency(correlation_score)
            })

        return correlated_threats

    def generate_threat_report(self, correlated_threats):
        """Generate comprehensive threat intelligence report"""
        report = {
            'executive_summary': self.generate_executive_summary(correlated_threats),
            'critical_findings': self.identify_critical_findings(correlated_threats),
            'risk_assessment': self.perform_risk_assessment(correlated_threats),
            'recommendations': self.generate_recommendations(correlated_threats),
            'timeline': self.create_remediation_timeline(correlated_threats)
        }

        return report`
        },
        {
          type: "heading",
          text: "Automated Threat Detection"
        },
        {
          type: "paragraph",
          text: "The framework includes automated detection capabilities for continuous threat monitoring:"
        },
        {
          type: "code",
          language: "python",
          code: `class AutomatedThreatDetection:
    def __init__(self):
        self.detection_rules = self.load_detection_rules()
        self.ml_models = self.initialize_ml_models()
        self.baseline_metrics = self.establish_baselines()

    def continuous_monitoring(self):
        """Implement continuous threat monitoring"""
        while True:
            try:
                # Collect security metrics
                current_metrics = self.collect_security_metrics()

                # Anomaly detection
                anomalies = self.detect_anomalies(current_metrics)

                # Pattern matching
                pattern_matches = self.pattern_matching(current_metrics)

                # Machine learning analysis
                ml_predictions = self.ml_threat_analysis(current_metrics)

                # Correlation analysis
                correlated_events = self.correlate_security_events(
                    anomalies, pattern_matches, ml_predictions
                )

                # Threat scoring
                threat_scores = self.calculate_threat_scores(correlated_events)

                # Alert generation
                if threat_scores:
                    self.generate_security_alerts(threat_scores)

                time.sleep(60)  # Monitor every minute

            except Exception as e:
                self.log_error(f"Monitoring error: {e}")
                time.sleep(300)  # Wait 5 minutes on error

    def detect_anomalies(self, metrics):
        """Detect statistical anomalies in security metrics"""
        anomalies = []

        for metric_name, current_value in metrics.items():
            baseline = self.baseline_metrics.get(metric_name)

            if baseline:
                # Statistical anomaly detection
                z_score = self.calculate_z_score(current_value, baseline)

                if abs(z_score) > 3:  # 3-sigma rule
                    anomalies.append({
                        'metric': metric_name,
                        'current_value': current_value,
                        'baseline': baseline,
                        'z_score': z_score,
                        'severity': self.determine_anomaly_severity(z_score)
                    })

        return anomalies

    def generate_security_alerts(self, threat_scores):
        """Generate and distribute security alerts"""
        for threat in threat_scores:
            if threat['score'] >= 8.0:  # Critical threshold
                alert = {
                    'severity': 'CRITICAL',
                    'threat_type': threat['type'],
                    'description': threat['description'],
                    'indicators': threat['indicators'],
                    'recommended_actions': threat['actions'],
                    'timestamp': time.time()
                }

                # Send immediate notification
                self.send_critical_alert(alert)

            elif threat['score'] >= 6.0:  # High threshold
                # Queue for high-priority review
                self.queue_high_priority_alert(threat)

            else:
                # Log for analysis
                self.log_security_event(threat)`
        },
        {
          type: "heading",
          text: "Implementation Guidelines"
        },
        {
          type: "paragraph",
          text: "To effectively implement this threat intelligence matrix in your security program:"
        },
        {
          type: "list",
          items: [
            "Establish baseline security metrics for your environment",
            "Integrate multiple threat intelligence feeds for comprehensive coverage",
            "Implement automated detection and alerting mechanisms",
            "Regularly update vulnerability classifications based on new intelligence",
            "Conduct periodic threat landscape assessments",
            "Maintain incident response procedures aligned with threat priorities",
            "Train security teams on matrix interpretation and response procedures"
          ]
        },
        {
          type: "quote",
          text: "Effective threat intelligence is not just about collecting data—it's about transforming information into actionable insights that drive security decisions and improve defensive capabilities.",
          author: "S.Tamilselvan, Security Researcher"
        },
        {
          type: "paragraph",
          text: "This comprehensive vulnerability classification and threat intelligence matrix provides security professionals with the tools and methodologies needed for effective risk assessment and threat prioritization. By combining quantitative metrics with real-time intelligence, organizations can make informed decisions about security investments and response strategies."
        }
      ],
      relatedPosts: ["security-research-framework", "advanced-osint-techniques", "web-penetration-testing-fundamentals"]
    },
    "advanced-osint-techniques": {
      title: "Advanced OSINT Techniques for Security Researchers",
      date: "January 5, 2024",
      readTime: "14 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "data:image/png;base64,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",
      content: [
        {
          type: "paragraph",
          text: "Open Source Intelligence (OSINT) gathering is a critical skill for security researchers, providing valuable insights into targets without direct interaction. This comprehensive guide, developed by S.Tamilselvan, covers advanced OSINT techniques, tools, and methodologies for professional security research and threat intelligence gathering."
        },
        {
          type: "heading",
          text: "Advanced Search Engine Intelligence"
        },
        {
          type: "paragraph",
          text: "Search engines contain vast amounts of indexed information that can be leveraged for intelligence gathering using advanced search operators and techniques:"
        },
        {
          type: "code",
          language: "text",
          code: `ADVANCED GOOGLE DORKING TECHNIQUES
=====================================

# Domain-specific intelligence gathering
site:target.com filetype:pdf
site:target.com inurl:admin
site:target.com intitle:"index of"
site:target.com intext:"password"
site:target.com cache:

# Technology stack identification
site:target.com "powered by"
site:target.com "built with"
site:target.com "running on"
site:target.com inurl:wp-content (WordPress)
site:target.com inurl:drupal (Drupal)

# Sensitive information discovery
site:target.com filetype:sql
site:target.com filetype:log
site:target.com filetype:bak
site:target.com "confidential" filetype:doc
site:target.com "internal use only"

# Employee and contact information
site:linkedin.com "target company"
site:target.com "@target.com"
"target.com" site:pastebin.com
"target.com" site:github.com

# Infrastructure and network information
site:target.com inurl:server-status
site:target.com inurl:server-info
site:target.com intitle:"Apache Status"
site:target.com intitle:"nginx status"

# Error messages and debug information
site:target.com "fatal error"
site:target.com "warning:" filetype:log
site:target.com "stack trace"
site:target.com intext:"mysql_connect"`
        },
        {
          type: "heading",
          text: "Social Media Intelligence (SOCMINT)"
        },
        {
          type: "paragraph",
          text: "Social media platforms provide rich sources of information about individuals, organizations, and their relationships. Advanced SOCMINT techniques include:"
        },
        {
          type: "code",
          language: "python",
          code: `#!/usr/bin/env python3
"""
Advanced Social Media Intelligence Framework
Author: S.Tamilselvan
Purpose: Automated social media reconnaissance and analysis
"""

import requests
import json
import time
import re
from datetime import datetime, timedelta
import tweepy
import facebook
import linkedin_api
from instagram_private_api import Client as InstagramClient

class SocialMediaIntelligence:
    def __init__(self):
        self.platforms = {
            'twitter': self.twitter_intelligence,
            'linkedin': self.linkedin_intelligence,
            'facebook': self.facebook_intelligence,
            'instagram': self.instagram_intelligence,
            'github': self.github_intelligence
        }
        self.results = {}

    def comprehensive_socmint(self, target_person, target_organization):
        """Execute comprehensive social media intelligence gathering"""
        print(f"[*] Starting SOCMINT for {target_person} / {target_organization}")

        for platform, method in self.platforms.items():
            try:
                print(f"[*] Gathering intelligence from {platform}...")
                platform_data = method(target_person, target_organization)
                self.results[platform] = platform_data
                time.sleep(2)  # Rate limiting
            except Exception as e:
                print(f"[!] Error gathering {platform} intelligence: {e}")
                self.results[platform] = {}

        return self.results

    def twitter_intelligence(self, person, organization):
        """Advanced Twitter intelligence gathering"""
        twitter_data = {
            'profiles': [],
            'tweets': [],
            'connections': [],
            'hashtags': [],
            'mentions': [],
            'geolocation_data': []
        }

        # Search queries for comprehensive coverage
        search_queries = [
            f'"{person}"',
            f'"{organization}"',
            f'from:{person}',
            f'to:{person}',
            f'#{organization.replace(" ", "")}',
            f'"{person}" "{organization}"'
        ]

        for query in search_queries:
            try:
                # Twitter API search implementation
                tweets = self.search_twitter(query, count=100)

                for tweet in tweets:
                    tweet_data = {
                        'id': tweet.id,
                        'text': tweet.text,
                        'user': tweet.user.screen_name,
                        'created_at': tweet.created_at,
                        'retweet_count': tweet.retweet_count,
                        'favorite_count': tweet.favorite_count,
                        'location': getattr(tweet, 'geo', None),
                        'hashtags': [tag['text'] for tag in tweet.entities.get('hashtags', [])],
                        'mentions': [mention['screen_name'] for mention in tweet.entities.get('user_mentions', [])],
                        'urls': [url['expanded_url'] for url in tweet.entities.get('urls', [])]
                    }
                    twitter_data['tweets'].append(tweet_data)

            except Exception as e:
                print(f"[!] Twitter search error: {e}")

        return twitter_data

    def linkedin_intelligence(self, person, organization):
        """LinkedIn professional intelligence gathering"""
        linkedin_data = {
            'profiles': [],
            'company_info': {},
            'employees': [],
            'connections': [],
            'posts': [],
            'skills': [],
            'education': []
        }

        try:
            # LinkedIn API implementation
            # Note: LinkedIn has strict API access requirements

            # Search for person profiles
            person_profiles = self.search_linkedin_profiles(person, organization)
            linkedin_data['profiles'] = person_profiles

            # Company information gathering
            company_info = self.get_linkedin_company_info(organization)
            linkedin_data['company_info'] = company_info

            # Employee enumeration
            employees = self.enumerate_linkedin_employees(organization)
            linkedin_data['employees'] = employees

        except Exception as e:
            print(f"[!] LinkedIn intelligence error: {e}")

        return linkedin_data

    def github_intelligence(self, person, organization):
        """GitHub code repository intelligence"""
        github_data = {
            'repositories': [],
            'commits': [],
            'issues': [],
            'collaborators': [],
            'organizations': [],
            'leaked_secrets': []
        }

        try:
            # GitHub API search
            search_queries = [
                f'"{person}" in:name',
                f'"{organization}" in:name',
                f'"{person}" in:description',
                f'"{organization}" in:description',
                f'"{person}" in:readme',
                f'"{organization}" in:readme'
            ]

            for query in search_queries:
                repos = self.search_github_repositories(query)

                for repo in repos:
                    repo_data = {
                        'name': repo['name'],
                        'full_name': repo['full_name'],
                        'description': repo['description'],
                        'url': repo['html_url'],
                        'language': repo['language'],
                        'stars': repo['stargazers_count'],
                        'forks': repo['forks_count'],
                        'created_at': repo['created_at'],
                        'updated_at': repo['updated_at'],
                        'owner': repo['owner']['login']
                    }

                    # Search for sensitive information in repository
                    secrets = self.scan_repository_secrets(repo['full_name'])
                    if secrets:
                        repo_data['potential_secrets'] = secrets

                    github_data['repositories'].append(repo_data)

        except Exception as e:
            print(f"[!] GitHub intelligence error: {e}")

        return github_data

    def scan_repository_secrets(self, repo_full_name):
        """Scan repository for potential secrets and sensitive information"""
        secrets = []

        # Common secret patterns
        secret_patterns = {
            'api_key': r'api[_-]?key["\']?\s*[:=]\s*["\']?([a-zA-Z0-9_-]+)',
            'password': r'password["\']?\s*[:=]\s*["\']?([^"\'\\s]+)',
            'secret': r'secret["\']?\s*[:=]\s*["\']?([a-zA-Z0-9_-]+)',
            'token': r'token["\']?\s*[:=]\s*["\']?([a-zA-Z0-9_-]+)',
            'aws_access_key': r'AKIA[0-9A-Z]{16}',
            'aws_secret_key': r'[0-9a-zA-Z/+]{40}',
            'private_key': r'-----BEGIN [A-Z]+ PRIVATE KEY-----',
            'database_url': r'(mysql|postgresql|mongodb)://[^\\s]+',
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}'
        }

        try:
            # Get repository contents
            contents = self.get_repository_contents(repo_full_name)

            for file_content in contents:
                for pattern_name, pattern in secret_patterns.items():
                    matches = re.findall(pattern, file_content['content'], re.IGNORECASE)

                    for match in matches:
                        secrets.append({
                            'type': pattern_name,
                            'value': match,
                            'file': file_content['path'],
                            'line': self.find_line_number(file_content['content'], match)
                        })

        except Exception as e:
            print(f"[!] Repository scanning error: {e}")

        return secrets`
        },
        {
          type: "heading",
          text: "Domain and Infrastructure Intelligence"
        },
        {
          type: "paragraph",
          text: "Advanced domain intelligence gathering provides insights into target infrastructure, relationships, and potential attack vectors:"
        },
        {
          type: "code",
          language: "python",
          code: `class DomainIntelligence:
    def __init__(self):
        self.whois_servers = {
            '.com': 'whois.verisign-grs.com',
            '.net': 'whois.verisign-grs.com',
            '.org': 'whois.pir.org',
            '.info': 'whois.afilias.net',
            '.biz': 'whois.neulevel.biz'
        }

    def comprehensive_domain_analysis(self, domain):
        """Comprehensive domain intelligence gathering"""
        domain_data = {
            'whois_info': {},
            'dns_records': {},
            'subdomains': [],
            'related_domains': [],
            'ssl_certificates': {},
            'historical_data': {},
            'reputation_data': {},
            'hosting_info': {}
        }

        # WHOIS analysis
        domain_data['whois_info'] = self.advanced_whois_analysis(domain)

        # DNS enumeration
        domain_data['dns_records'] = self.comprehensive_dns_analysis(domain)

        # Subdomain discovery
        domain_data['subdomains'] = self.advanced_subdomain_discovery(domain)

        # Related domain discovery
        domain_data['related_domains'] = self.discover_related_domains(domain)

        # SSL certificate analysis
        domain_data['ssl_certificates'] = self.ssl_certificate_analysis(domain)

        # Historical domain data
        domain_data['historical_data'] = self.historical_domain_analysis(domain)

        return domain_data

    def advanced_whois_analysis(self, domain):
        """Advanced WHOIS information analysis"""
        whois_data = {}

        try:
            import whois
            w = whois.whois(domain)

            whois_data = {
                'registrar': w.registrar,
                'creation_date': str(w.creation_date),
                'expiration_date': str(w.expiration_date),
                'name_servers': w.name_servers,
                'registrant_name': w.name,
                'registrant_org': w.org,
                'registrant_email': w.emails,
                'registrant_country': w.country,
                'admin_email': w.emails,
                'tech_email': w.emails,
                'status': w.status
            }

            # Privacy protection detection
            privacy_indicators = [
                'privacy', 'protection', 'proxy', 'whoisguard',
                'domains by proxy', 'perfect privacy'
            ]

            whois_text = str(w).lower()
            whois_data['privacy_protected'] = any(
                indicator in whois_text for indicator in privacy_indicators
            )

        except Exception as e:
            print(f"[!] WHOIS analysis error: {e}")

        return whois_data

    def discover_related_domains(self, domain):
        """Discover related domains using various techniques"""
        related_domains = set()

        try:
            # Reverse WHOIS lookup
            whois_info = self.advanced_whois_analysis(domain)
            if whois_info.get('registrant_email'):
                email_domains = self.reverse_whois_by_email(whois_info['registrant_email'])
                related_domains.update(email_domains)

            # Certificate transparency logs
            ct_domains = self.certificate_transparency_domains(domain)
            related_domains.update(ct_domains)

            # DNS similarity analysis
            similar_domains = self.find_similar_domains(domain)
            related_domains.update(similar_domains)

            # Typosquatting detection
            typo_domains = self.generate_typosquatting_domains(domain)
            existing_typos = self.check_domain_existence(typo_domains)
            related_domains.update(existing_typos)

        except Exception as e:
            print(f"[!] Related domain discovery error: {e}")

        return list(related_domains)

    def historical_domain_analysis(self, domain):
        """Analyze historical domain data"""
        historical_data = {}

        try:
            # Wayback Machine analysis
            wayback_data = self.wayback_machine_analysis(domain)
            historical_data['wayback_snapshots'] = wayback_data

            # Historical DNS records
            historical_dns = self.historical_dns_analysis(domain)
            historical_data['historical_dns'] = historical_dns

            # Domain reputation history
            reputation_history = self.domain_reputation_history(domain)
            historical_data['reputation_history'] = reputation_history

        except Exception as e:
            print(f"[!] Historical analysis error: {e}")

        return historical_data

    def wayback_machine_analysis(self, domain):
        """Analyze Wayback Machine snapshots"""
        wayback_data = []

        try:
            # Query Wayback Machine API
            url = f"http://web.archive.org/cdx/search/cdx?url={domain}/*&output=json&limit=100"
            response = requests.get(url, timeout=30)

            if response.status_code == 200:
                data = response.json()

                for entry in data[1:]:  # Skip header
                    snapshot = {
                        'timestamp': entry[1],
                        'url': entry[2],
                        'status_code': entry[4],
                        'mime_type': entry[3],
                        'length': entry[5]
                    }
                    wayback_data.append(snapshot)

        except Exception as e:
            print(f"[!] Wayback Machine analysis error: {e}")

        return wayback_data`
        },
        {
          type: "heading",
          text: "Automated OSINT Workflows"
        },
        {
          type: "paragraph",
          text: "Automation is crucial for efficient OSINT operations. The following framework demonstrates automated intelligence gathering workflows:"
        },
        {
          type: "code",
          language: "python",
          code: `class AutomatedOSINTWorkflow:
    def __init__(self):
        self.intelligence_sources = [
            'search_engines',
            'social_media',
            'domain_intelligence',
            'certificate_transparency',
            'code_repositories',
            'paste_sites',
            'breach_databases',
            'threat_intelligence_feeds'
        ]
        self.results = {}

    def execute_automated_workflow(self, target):
        """Execute comprehensive automated OSINT workflow"""
        print(f"[*] Starting automated OSINT workflow for {target}")

        # Phase 1: Initial reconnaissance
        initial_data = self.initial_reconnaissance(target)

        # Phase 2: Deep intelligence gathering
        deep_intelligence = self.deep_intelligence_gathering(target, initial_data)

        # Phase 3: Correlation and analysis
        correlated_data = self.correlate_intelligence(deep_intelligence)

        # Phase 4: Report generation
        final_report = self.generate_intelligence_report(correlated_data)

        return final_report

    def initial_reconnaissance(self, target):
        """Initial reconnaissance phase"""
        initial_data = {}

        # Basic domain information
        if self.is_domain(target):
            initial_data['domain_info'] = self.basic_domain_info(target)
            initial_data['subdomains'] = self.quick_subdomain_scan(target)

        # Basic person information
        elif self.is_person(target):
            initial_data['person_info'] = self.basic_person_info(target)
            initial_data['social_profiles'] = self.quick_social_scan(target)

        # Basic organization information
        elif self.is_organization(target):
            initial_data['org_info'] = self.basic_org_info(target)
            initial_data['domains'] = self.find_org_domains(target)

        return initial_data

    def deep_intelligence_gathering(self, target, initial_data):
        """Deep intelligence gathering phase"""
        deep_data = {}

        # Expand based on initial findings
        if 'domain_info' in initial_data:
            deep_data['comprehensive_domain'] = self.comprehensive_domain_analysis(target)

            # Analyze discovered subdomains
            for subdomain in initial_data.get('subdomains', []):
                deep_data[f'subdomain_{subdomain}'] = self.analyze_subdomain(subdomain)

        if 'person_info' in initial_data:
            deep_data['comprehensive_person'] = self.comprehensive_person_analysis(target)

            # Analyze social profiles
            for profile in initial_data.get('social_profiles', []):
                deep_data[f'profile_{profile["platform"]}'] = self.analyze_social_profile(profile)

        # Cross-reference findings
        deep_data['cross_references'] = self.cross_reference_findings(initial_data, deep_data)

        return deep_data

    def correlate_intelligence(self, intelligence_data):
        """Correlate and analyze gathered intelligence"""
        correlations = {
            'entity_relationships': [],
            'infrastructure_connections': [],
            'temporal_patterns': [],
            'behavioral_patterns': [],
            'risk_indicators': []
        }

        # Entity relationship analysis
        correlations['entity_relationships'] = self.analyze_entity_relationships(intelligence_data)

        # Infrastructure correlation
        correlations['infrastructure_connections'] = self.correlate_infrastructure(intelligence_data)

        # Temporal analysis
        correlations['temporal_patterns'] = self.analyze_temporal_patterns(intelligence_data)

        # Risk assessment
        correlations['risk_indicators'] = self.assess_risk_indicators(intelligence_data)

        return correlations

    def generate_intelligence_report(self, correlated_data):
        """Generate comprehensive intelligence report"""
        report = {
            'executive_summary': self.generate_executive_summary(correlated_data),
            'key_findings': self.extract_key_findings(correlated_data),
            'risk_assessment': self.generate_risk_assessment(correlated_data),
            'recommendations': self.generate_recommendations(correlated_data),
            'technical_details': correlated_data,
            'timeline': self.create_intelligence_timeline(correlated_data),
            'appendices': self.create_appendices(correlated_data)
        }

        return report`
        },
        {
          type: "heading",
          text: "OSINT Tools and Resources"
        },
        {
          type: "paragraph",
          text: "Professional OSINT operations require a comprehensive toolkit. Essential tools and resources include:"
        },
        {
          type: "list",
          items: [
            "Search Engines: Google, Bing, DuckDuckGo, Yandex, Baidu with advanced operators",
            "Social Media Tools: Twint, Social-Analyzer, Sherlock, WhatsMyName",
            "Domain Intelligence: Whois, DNSRecon, Sublist3r, Amass, Subfinder",
            "Certificate Analysis: crt.sh, Censys, Shodan, Certificate Transparency logs",
            "Code Repository Search: GitHub, GitLab, Bitbucket, SourceForge",
            "Breach Data: HaveIBeenPwned, DeHashed, Breach-Parse",
            "Threat Intelligence: MISP, OpenCTI, ThreatCrowd, VirusTotal",
            "Automation Frameworks: Recon-ng, theHarvester, SpiderFoot, Maltego"
          ]
        },
        {
          type: "heading",
          text: "Legal and Ethical Considerations"
        },
        {
          type: "paragraph",
          text: "OSINT operations must be conducted within legal and ethical boundaries:"
        },
        {
          type: "list",
          items: [
            "Respect privacy laws and regulations (GDPR, CCPA, etc.)",
            "Obtain proper authorization for intelligence gathering activities",
            "Use only publicly available information sources",
            "Avoid social engineering or deceptive practices",
            "Maintain data confidentiality and secure storage",
            "Follow responsible disclosure practices",
            "Document all activities for audit and compliance purposes"
          ]
        },
        {
          type: "quote",
          text: "OSINT is not about finding hidden secrets—it's about connecting publicly available information in meaningful ways to generate actionable intelligence while respecting privacy and legal boundaries.",
          author: "S.Tamilselvan, Security Researcher"
        },
        {
          type: "paragraph",
          text: "Advanced OSINT techniques provide security researchers with powerful capabilities for intelligence gathering and threat analysis. By combining automated tools with manual analysis techniques, researchers can efficiently gather comprehensive intelligence while maintaining ethical standards and legal compliance."
        }
      ],
      relatedPosts: ["security-research-framework", "threat-intelligence-matrix", "web-penetration-testing-fundamentals"]
    },
    "web-penetration-testing-fundamentals": {
      title: "Web Penetration Testing Fundamentals: A Complete Guide",
      date: "December 15, 2023",
      readTime: "12 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://datami.ee/assets/images/cache/assets/images/blog/eng_pen_test_steps-1-1478x986.png",
      content: [
        {
          type: "paragraph",
          text: "Web application penetration testing is a critical security practice that involves systematically testing web applications for vulnerabilities that could be exploited by malicious actors. This comprehensive guide covers the fundamental methodologies, tools, and best practices essential for effective web penetration testing."
        },
        {
          type: "heading",
          text: "Understanding Web Application Security"
        },
        {
          type: "paragraph",
          text: "Web applications face numerous security threats due to their complexity and exposure to the internet. Common vulnerabilities include injection flaws, broken authentication, sensitive data exposure, and security misconfigurations. Understanding these threats is the first step in effective penetration testing."
        },
        {
          type: "heading",
          text: "Penetration Testing Methodology"
        },
        {
          type: "paragraph",
          text: "A structured approach to web penetration testing typically follows these phases:"
        },
        {
          type: "list",
          items: [
            "Reconnaissance and Information Gathering",
            "Vulnerability Scanning and Analysis",
            "Exploitation and Proof of Concept",
            "Post-Exploitation and Privilege Escalation",
            "Documentation and Reporting"
          ]
        },
        {
          type: "heading",
          text: "Essential Tools for Web Penetration Testing"
        },
        {
          type: "paragraph",
          text: "Professional penetration testers rely on a variety of tools to identify and exploit vulnerabilities:"
        },
        {
          type: "list",
          items: [
            "Burp Suite Professional - Comprehensive web application security testing",
            "OWASP ZAP - Free and open-source web application scanner",
            "Nmap - Network discovery and security auditing",
            "SQLMap - Automated SQL injection testing tool",
            "Nikto - Web server scanner for vulnerabilities",
            "Metasploit - Exploitation framework for penetration testing"
          ]
        },
        {
          type: "heading",
          text: "Common Web Application Vulnerabilities"
        },
        {
          type: "paragraph",
          text: "Based on the OWASP Top 10, the most critical web application security risks include:"
        },
        {
          type: "list",
          items: [
            "Injection vulnerabilities (SQL, NoSQL, OS command)",
            "Broken Authentication and Session Management",
            "Sensitive Data Exposure",
            "XML External Entities (XXE)",
            "Broken Access Control",
            "Security Misconfigurations",
            "Cross-Site Scripting (XSS)",
            "Insecure Deserialization",
            "Using Components with Known Vulnerabilities",
            "Insufficient Logging and Monitoring"
          ]
        },
        {
          type: "heading",
          text: "Best Practices for Penetration Testing"
        },
        {
          type: "paragraph",
          text: "Effective web penetration testing requires adherence to industry best practices:"
        },
        {
          type: "list",
          items: [
            "Obtain proper authorization before testing",
            "Follow a systematic methodology",
            "Document all findings with proof of concept",
            "Prioritize vulnerabilities by risk level",
            "Provide actionable remediation recommendations",
            "Maintain confidentiality of sensitive information",
            "Stay updated with latest security threats and techniques"
          ]
        },
        {
          type: "quote",
          text: "The goal of penetration testing is not just to find vulnerabilities, but to understand the real-world impact of security weaknesses and provide organizations with actionable insights to improve their security posture.",
          author: "OWASP Testing Guide"
        },
        {
          type: "paragraph",
          text: "Regular web application penetration testing is essential for maintaining a strong security posture in today's threat landscape. By following established methodologies and using appropriate tools, organizations can identify and remediate vulnerabilities before they can be exploited by malicious actors."
        }
      ],
      relatedPosts: ["owasp-top-10-2023", "vulnerability-assessment-methodology", "cybersecurity-frameworks-compliance"]
    },
    "owasp-top-10-2023": {
      title: "OWASP Top 10 2023: Critical Web Application Security Risks",
      date: "November 28, 2023",
      readTime: "10 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://www.appknox.com/hs-fs/hubfs/How%20to%20successfully%20conduct%20penetration%20testing_%20(1).png?width=2324&height=3032&name=How%20to%20successfully%20conduct%20penetration%20testing_%20(1).png",
      content: [
        {
          type: "paragraph",
          text: "The OWASP Top 10 represents the most critical security risks to web applications. Updated regularly by security experts worldwide, this list serves as a fundamental awareness document for developers, security professionals, and organizations building web applications."
        },
        {
          type: "heading",
          text: "A01:2021 – Broken Access Control"
        },
        {
          type: "paragraph",
          text: "Access control enforces policy such that users cannot act outside of their intended permissions. Failures typically lead to unauthorized information disclosure, modification, or destruction of data."
        },
        {
          type: "list",
          items: [
            "Violation of the principle of least privilege",
            "Bypassing access control checks by modifying URLs",
            "Elevation of privilege without proper authorization",
            "Metadata manipulation such as replaying JWT tokens"
          ]
        },
        {
          type: "heading",
          text: "A02:2021 – Cryptographic Failures"
        },
        {
          type: "paragraph",
          text: "Previously known as Sensitive Data Exposure, this category focuses on failures related to cryptography which often leads to sensitive data exposure or system compromise."
        },
        {
          type: "heading",
          text: "A03:2021 – Injection"
        },
        {
          type: "paragraph",
          text: "An application is vulnerable to injection attacks when user-supplied data is not validated, filtered, or sanitized by the application."
        },
        {
          type: "list",
          items: [
            "SQL injection",
            "NoSQL injection",
            "OS command injection",
            "LDAP injection",
            "Expression Language (EL) injection"
          ]
        },
        {
          type: "heading",
          text: "A04:2021 – Insecure Design"
        },
        {
          type: "paragraph",
          text: "Insecure design is a broad category representing different weaknesses, expressed as 'missing or ineffective control design.' Secure design is a culture and methodology that constantly evaluates threats."
        },
        {
          type: "heading",
          text: "A05:2021 – Security Misconfiguration"
        },
        {
          type: "paragraph",
          text: "Security misconfiguration is commonly a result of insecure default configurations, incomplete configurations, open cloud storage, misconfigured HTTP headers, and verbose error messages."
        },
        {
          type: "heading",
          text: "Mitigation Strategies"
        },
        {
          type: "paragraph",
          text: "Protecting against OWASP Top 10 vulnerabilities requires a comprehensive approach:"
        },
        {
          type: "list",
          items: [
            "Implement secure coding practices",
            "Regular security testing and code reviews",
            "Use security frameworks and libraries",
            "Apply the principle of least privilege",
            "Implement proper input validation and sanitization",
            "Use secure configuration management",
            "Regular security training for development teams"
          ]
        },
        {
          type: "quote",
          text: "Security is not a product, but a process. It's more than adding a lock to the door; it's about building the entire house with security in mind.",
          author: "Bruce Schneier"
        }
      ],
      relatedPosts: ["web-penetration-testing-fundamentals", "vulnerability-assessment-methodology"]
    },
    "cybersecurity-frameworks-compliance": {
      title: "Cybersecurity Frameworks and Compliance Standards",
      date: "October 20, 2023",
      readTime: "8 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://sprinto.com/wp-content/uploads/2023/04/Copy-of-Blog_164_Cyber-Security-Framework-All-You-Need-To-Know-In-2023_v-02.jpg",
      content: [
        {
          type: "paragraph",
          text: "Cybersecurity frameworks and compliance standards provide organizations with structured approaches to managing cybersecurity risks. Understanding these frameworks is essential for building robust security programs and meeting regulatory requirements."
        },
        {
          type: "heading",
          text: "NIST Cybersecurity Framework"
        },
        {
          type: "paragraph",
          text: "The National Institute of Standards and Technology (NIST) Cybersecurity Framework provides a policy framework of computer security guidance for how private sector organizations can assess and improve their ability to prevent, detect, and respond to cyber attacks."
        },
        {
          type: "list",
          items: [
            "Identify - Develop organizational understanding of cybersecurity risk",
            "Protect - Implement appropriate safeguards to ensure delivery of services",
            "Detect - Develop and implement activities to identify cybersecurity events",
            "Respond - Take action regarding a detected cybersecurity incident",
            "Recover - Maintain plans for resilience and restore capabilities"
          ]
        },
        {
          type: "heading",
          text: "ISO 27001 Information Security Management"
        },
        {
          type: "paragraph",
          text: "ISO 27001 is an international standard that provides requirements for establishing, implementing, maintaining, and continually improving an information security management system (ISMS)."
        },
        {
          type: "list",
          items: [
            "Risk assessment and treatment methodology",
            "Security policy and objectives",
            "Asset management and classification",
            "Access control and identity management",
            "Incident response and business continuity",
            "Supplier relationship security"
          ]
        },
        {
          type: "heading",
          text: "SOC 2 Compliance"
        },
        {
          type: "paragraph",
          text: "Service Organization Control (SOC) 2 is an auditing procedure that ensures service providers securely manage data to protect the interests of the organization and the privacy of its clients."
        },
        {
          type: "list",
          items: [
            "Security - Protection against unauthorized access",
            "Availability - System operation and usability as committed",
            "Processing Integrity - System processing completeness and accuracy",
            "Confidentiality - Protection of confidential information",
            "Privacy - Personal information collection and processing"
          ]
        },
        {
          type: "heading",
          text: "Implementation Best Practices"
        },
        {
          type: "paragraph",
          text: "Successfully implementing cybersecurity frameworks requires a strategic approach:"
        },
        {
          type: "list",
          items: [
            "Conduct thorough risk assessments",
            "Align framework selection with business objectives",
            "Establish clear governance and accountability",
            "Implement continuous monitoring and improvement",
            "Provide regular training and awareness programs",
            "Maintain documentation and evidence collection"
          ]
        },
        {
          type: "quote",
          text: "Compliance is not a destination but a journey. Organizations must continuously adapt their security posture to address evolving threats and regulatory requirements.",
          author: "Cybersecurity and Infrastructure Security Agency (CISA)"
        }
      ],
      relatedPosts: ["web-penetration-testing-fundamentals", "owasp-top-10-2023"]
    },
    "vulnerability-assessment-methodology": {
      title: "Vulnerability Assessment Methodology and Best Practices",
      date: "September 12, 2023",
      readTime: "9 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://webdesignsof.com/wp-content/uploads/2023/09/Web-Application-Security-Testing_jpeq-1024x355-1.jpg",
      content: [
        {
          type: "paragraph",
          text: "Vulnerability assessment is a systematic process of identifying, quantifying, and prioritizing security vulnerabilities in systems, applications, and networks. This comprehensive methodology helps organizations understand their security posture and implement effective remediation strategies."
        },
        {
          type: "heading",
          text: "Vulnerability Assessment vs Penetration Testing"
        },
        {
          type: "paragraph",
          text: "While often confused, vulnerability assessments and penetration testing serve different purposes:"
        },
        {
          type: "list",
          items: [
            "Vulnerability Assessment: Automated scanning to identify known vulnerabilities",
            "Penetration Testing: Manual exploitation to demonstrate real-world impact",
            "Scope: VA covers broader surface area, PT focuses on specific targets",
            "Depth: VA provides breadth of coverage, PT provides depth of analysis"
          ]
        },
        {
          type: "heading",
          text: "Assessment Methodology"
        },
        {
          type: "paragraph",
          text: "A structured vulnerability assessment follows these key phases:"
        },
        {
          type: "list",
          items: [
            "Planning and Scoping - Define assessment objectives and boundaries",
            "Asset Discovery - Identify all systems and applications in scope",
            "Vulnerability Scanning - Use automated tools to detect vulnerabilities",
            "Manual Verification - Validate findings and reduce false positives",
            "Risk Analysis - Assess impact and likelihood of exploitation",
            "Reporting and Remediation - Document findings with actionable recommendations"
          ]
        },
        {
          type: "heading",
          text: "Essential Scanning Tools"
        },
        {
          type: "paragraph",
          text: "Professional vulnerability assessments utilize various scanning tools:"
        },
        {
          type: "list",
          items: [
            "Nessus - Comprehensive vulnerability scanner",
            "OpenVAS - Open-source vulnerability assessment tool",
            "Qualys VMDR - Cloud-based vulnerability management",
            "Rapid7 Nexpose - Enterprise vulnerability management",
            "Nmap - Network discovery and security auditing",
            "Nuclei - Fast and customizable vulnerability scanner"
          ]
        },
        {
          type: "heading",
          text: "Risk Prioritization Framework"
        },
        {
          type: "paragraph",
          text: "Effective vulnerability management requires proper risk prioritization using frameworks like CVSS (Common Vulnerability Scoring System):"
        },
        {
          type: "list",
          items: [
            "Critical (9.0-10.0) - Immediate remediation required",
            "High (7.0-8.9) - Remediate within 30 days",
            "Medium (4.0-6.9) - Remediate within 90 days",
            "Low (0.1-3.9) - Remediate within next maintenance window",
            "Consider business context and asset criticality"
          ]
        },
        {
          type: "heading",
          text: "Continuous Vulnerability Management"
        },
        {
          type: "paragraph",
          text: "Modern vulnerability management requires a continuous approach rather than point-in-time assessments:"
        },
        {
          type: "list",
          items: [
            "Automated scanning schedules",
            "Real-time threat intelligence integration",
            "Asset inventory management",
            "Patch management integration",
            "Metrics and KPI tracking",
            "Regular program maturity assessments"
          ]
        },
        {
          type: "quote",
          text: "The goal of vulnerability assessment is not to find every possible vulnerability, but to identify and prioritize the most critical risks that could impact business operations.",
          author: "SANS Institute"
        }
      ],
      relatedPosts: ["web-penetration-testing-fundamentals", "owasp-top-10-2023", "cybersecurity-frameworks-compliance"]
    },
    "security-auditing-best-practices": {
      title: "Security Auditing Best Practices for Enterprise Systems",
      date: "August 5, 2023",
      readTime: "11 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://cdn-fhlmj.nitrocdn.com/zDOoeLRMxwfUckhQvngSdCSTNUWTWaTK/assets/images/optimized/rev-2583352/www.your-itdepartment.co.uk/wp-content/uploads/2024/10/Cyber-Security-Audit-Checklist.png",
      content: [
        {
          type: "paragraph",
          text: "Security auditing is a systematic evaluation of an organization's information systems, policies, and procedures to ensure compliance with security standards and identify potential vulnerabilities. This comprehensive guide covers enterprise-level security auditing methodologies and best practices."
        },
        {
          type: "heading",
          text: "Types of Security Audits"
        },
        {
          type: "paragraph",
          text: "Security audits can be categorized into several types based on scope and objectives:"
        },
        {
          type: "list",
          items: [
            "Compliance Audits - Verify adherence to regulatory requirements",
            "Operational Audits - Assess day-to-day security operations",
            "Technical Audits - Evaluate technical security controls",
            "Physical Security Audits - Review physical access controls",
            "Third-Party Audits - Assess vendor and supplier security"
          ]
        },
        {
          type: "heading",
          text: "Audit Planning and Preparation"
        },
        {
          type: "paragraph",
          text: "Effective security audits require thorough planning and preparation:"
        },
        {
          type: "list",
          items: [
            "Define audit scope and objectives",
            "Identify applicable standards and regulations",
            "Assemble qualified audit team",
            "Develop audit timeline and milestones",
            "Prepare audit tools and documentation templates",
            "Coordinate with stakeholders and system owners"
          ]
        },
        {
          type: "heading",
          text: "Audit Execution Methodology"
        },
        {
          type: "paragraph",
          text: "The audit execution phase follows a structured approach:"
        },
        {
          type: "list",
          items: [
            "Document review and analysis",
            "Interviews with key personnel",
            "Technical testing and validation",
            "Evidence collection and documentation",
            "Gap analysis and risk assessment",
            "Preliminary findings discussion"
          ]
        },
        {
          type: "heading",
          text: "Common Audit Findings"
        },
        {
          type: "paragraph",
          text: "Typical security audit findings include:"
        },
        {
          type: "list",
          items: [
            "Inadequate access controls and user management",
            "Missing or outdated security policies",
            "Insufficient logging and monitoring",
            "Unpatched systems and software",
            "Weak password policies and practices",
            "Inadequate incident response procedures"
          ]
        },
        {
          type: "quote",
          text: "The value of a security audit lies not just in identifying what's wrong, but in providing a roadmap for continuous security improvement.",
          author: "ISACA Security Audit Guidelines"
        }
      ],
      relatedPosts: ["cybersecurity-frameworks-compliance", "vulnerability-assessment-methodology"]
    },
    "incident-response-playbook": {
      title: "Incident Response Playbook: From Detection to Recovery",
      date: "July 18, 2023",
      readTime: "13 min read",
      author: "S.Tamilselvan",
      category: "Cybersecurity",
      imageSrc: "https://www.incidentresponse.com/mini-sites/workflows/img/UnauthAccess-detect.jpg",
      content: [
        {
          type: "paragraph",
          text: "An effective incident response playbook is crucial for minimizing the impact of cybersecurity incidents. This comprehensive guide covers the essential phases of incident response, from initial detection through full recovery and lessons learned."
        },
        {
          type: "heading",
          text: "Incident Response Lifecycle"
        },
        {
          type: "paragraph",
          text: "The NIST incident response lifecycle consists of four key phases:"
        },
        {
          type: "list",
          items: [
            "Preparation - Establish IR capabilities and procedures",
            "Detection and Analysis - Identify and assess incidents",
            "Containment, Eradication, and Recovery - Respond to incidents",
            "Post-Incident Activity - Learn and improve from incidents"
          ]
        },
        {
          type: "heading",
          text: "Incident Classification and Prioritization"
        },
        {
          type: "paragraph",
          text: "Proper incident classification ensures appropriate response levels:"
        },
        {
          type: "list",
          items: [
            "Critical - Immediate threat to business operations",
            "High - Significant impact on systems or data",
            "Medium - Limited impact with potential for escalation",
            "Low - Minimal impact on operations",
            "Consider business impact, data sensitivity, and regulatory requirements"
          ]
        },
        {
          type: "heading",
          text: "Response Team Structure"
        },
        {
          type: "paragraph",
          text: "Effective incident response requires a well-structured team:"
        },
        {
          type: "list",
          items: [
            "Incident Commander - Overall response coordination",
            "Security Analysts - Technical investigation and analysis",
            "IT Operations - System administration and recovery",
            "Legal Counsel - Regulatory and legal considerations",
            "Communications - Internal and external communications",
            "Management - Executive decision making and resource allocation"
          ]
        },
        {
          type: "heading",
          text: "Containment Strategies"
        },
        {
          type: "paragraph",
          text: "Containment strategies vary based on incident type and impact:"
        },
        {
          type: "list",
          items: [
            "Network isolation and segmentation",
            "Account disabling and password resets",
            "System shutdown and preservation",
            "Evidence collection and forensic imaging",
            "Communication restrictions and monitoring",
            "Backup system activation"
          ]
        },
        {
          type: "heading",
          text: "Recovery and Lessons Learned"
        },
        {
          type: "paragraph",
          text: "The recovery phase focuses on restoring normal operations and improving future response:"
        },
        {
          type: "list",
          items: [
            "System restoration and validation",
            "Enhanced monitoring implementation",
            "Post-incident review and documentation",
            "Process improvement recommendations",
            "Training updates and awareness programs",
            "Playbook refinement and testing"
          ]
        },
        {
          type: "quote",
          text: "The best incident response is the one that never has to be used, but when it is needed, preparation and practice make all the difference.",
          author: "SANS Incident Response Team"
        }
      ],
      relatedPosts: ["security-auditing-best-practices", "cybersecurity-frameworks-compliance"]
    }
  };
  
  return blogPosts[id as keyof typeof blogPosts];
};

const BlogDetail = () => {
  const { id } = useParams<{ id: string }>();
  const post = id ? getBlogPostById(id) : null;
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [id]);
  
  if (!post) {
    return (
      <div className="pt-32 pb-16 px-6 md:px-12 text-center">
        <h1 className="text-2xl font-bold text-white mb-4">Blog post not found</h1>
        <Link to="/blog" className="text-psyco-green-DEFAULT hover:text-psyco-green-light">
          Return to blog
        </Link>
      </div>
    );
  }

  return (
    <div className="pt-32 pb-16">
      {/* Back button */}
      <div className="max-w-4xl mx-auto px-6 md:px-12 mb-8">
        <Link 
          to="/blog" 
          className="inline-flex items-center text-psyco-green-DEFAULT hover:text-psyco-green-light transition-colors"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to all articles
        </Link>
      </div>
      
      {/* Article header */}
      <article className="max-w-4xl mx-auto px-6 md:px-12">
        <header className="mb-8">
          <div className="mb-4">
            <span className="bg-psyco-green-DEFAULT px-3 py-1 text-xs font-medium text-white rounded-full">
              {post.category}
            </span>
          </div>
          
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-6">
            {post.title}
          </h1>
          
          <div className="flex flex-wrap items-center text-sm text-gray-400 gap-4 md:gap-6">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              <span>{post.date}</span>
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              <span>{post.readTime}</span>
            </div>
            <div className="flex items-center">
              <User className="h-4 w-4 mr-2" />
              <span>{post.author}</span>
            </div>
          </div>
        </header>
        
        {/* Featured image */}
        <div className="mb-10 rounded-lg overflow-hidden">
          <img 
            src={post.imageSrc} 
            alt={post.title} 
            className="w-full h-auto"
          />
        </div>
        
        {/* Article content */}
        <div className="prose prose-invert max-w-none">
          {post.content.map((section, index) => {
            if (section.type === "paragraph") {
              return <p key={index} className="text-gray-300 mb-6">{section.text}</p>;
            } else if (section.type === "heading") {
              return <h2 key={index} className="text-2xl font-bold text-white mt-10 mb-4">{section.text}</h2>;
            } else if (section.type === "list") {
              return (
                <ul key={index} className="list-disc pl-6 mb-6 text-gray-300">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="mb-2">{item}</li>
                  ))}
                </ul>
              );
            } else if (section.type === "code") {
              return (
                <div key={index} className="bg-gray-900 rounded-lg p-4 mb-6 overflow-x-auto">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-psyco-green-DEFAULT text-sm font-medium">{section.language}</span>
                    <span className="text-gray-400 text-xs">Code Example</span>
                  </div>
                  <pre className="text-gray-300 text-sm">
                    <code>{section.code}</code>
                  </pre>
                </div>
              );
            } else if (section.type === "quote") {
              return (
                <blockquote key={index} className="border-l-4 border-psyco-green-DEFAULT pl-4 italic my-6">
                  <p className="text-gray-300 mb-2">"{section.text}"</p>
                  {section.author && (
                    <footer className="text-sm text-gray-400">— {section.author}</footer>
                  )}
                </blockquote>
              );
            }
            return null;
          })}
        </div>
        
        {/* Share buttons */}
        <div className="mt-12 pt-6 border-t border-gray-800">
          <div className="flex items-center">
            <span className="text-gray-400 mr-4">Share this article:</span>
            <div className="flex space-x-3">
              <button className="text-gray-400 hover:text-psyco-green-DEFAULT transition-colors">
                <Share2 size={18} />
              </button>
            </div>
          </div>
        </div>
        
        {/* Related posts */}
        {post.relatedPosts && post.relatedPosts.length > 0 && (
          <div className="mt-16">
            <h3 className="text-xl font-bold text-white mb-6">Related Articles</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {post.relatedPosts.map((relatedId) => {
                const relatedPost = getBlogPostById(relatedId);
                if (!relatedPost) return null;
                
                return (
                  <div key={relatedId} className="glassmorphism overflow-hidden card-hover">
                    <Link to={`/blog/${relatedId}`} className="block">
                      <div className="p-6">
                        <h4 className="text-lg font-medium text-white mb-2 hover:text-psyco-green-light transition-colors">
                          {relatedPost.title}
                        </h4>
                      </div>
                    </Link>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </article>
    </div>
  );
};

export default BlogDetail;
