# 🚨 Permission Denied Error - Quick Fix Guide

## Problem
Getting "Permission denied" error when trying to create certificates.

## 🚀 Quick Solution (5 minutes)

### Step 1: Open Firebase Console
1. Go to [Firebase Console](https://console.firebase.google.com/project/certificate-generator-wolf)
2. Click on your project: `certificate-generator-wolf`

### Step 2: Update Firestore Rules
1. In the left sidebar, click **"Firestore Database"**
2. Click the **"Rules"** tab at the top
3. You'll see the current rules - **replace everything** with this:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /certificates/{document} {
      allow read, write: if true;
    }
  }
}
```

4. Click **"Publish"** button
5. Wait for "Rules published successfully" message

### Step 3: Test Certificate Creation
1. Go back to your website
2. Navigate to `/certificate-create`
3. Login with admin credentials
4. Try creating a certificate
5. Should work now! ✅

## 🔒 Security Note
The above rules allow anyone to read/write certificates (for testing only).

**For production**, use these secure rules instead:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /certificates/{document} {
      allow read: if true;
      allow write: if request.auth != null && 
                   request.auth.token.email == "<EMAIL>";
    }
  }
}
```

## 🔧 If Still Not Working

### Check Authentication Setup
1. In Firebase Console, go to **"Authentication"**
2. Click **"Users"** tab
3. Make sure user exists: `<EMAIL>`
4. If not, click **"Add user"** and create it with password: `Tamilselvanadmin6379869678@`

### Enable Authentication
1. In Firebase Console, go to **"Authentication"**
2. Click **"Get started"** if not already enabled
3. Go to **"Sign-in method"** tab
4. Enable **"Email/Password"** provider
5. Save changes

### Check Database Creation
1. In Firebase Console, go to **"Firestore Database"**
2. If you see "Get started", click it
3. Choose **"Start in test mode"**
4. Select location (recommend `asia-south1` for India)
5. Click **"Done"**

## 📱 Test Steps
1. Open browser developer tools (F12)
2. Go to Console tab
3. Try creating certificate
4. Check for any error messages
5. If you see specific errors, check the troubleshooting section below

## 🐛 Common Error Messages & Solutions

### "Firebase not configured"
- Check `src/lib/firebase.ts` has correct config
- Make sure `npm install firebase` was run

### "Admin user not found"
- Create admin user in Firebase Authentication
- Use exact email: `<EMAIL>`

### "Permission denied"
- Use the development rules shown above
- Make sure rules are published in Firebase Console

### "Service unavailable"
- Check internet connection
- Try again in a few minutes
- Check Firebase status page

## 📞 Still Need Help?
1. Check browser console for detailed errors
2. Verify all steps in `FIREBASE_SETUP.md`
3. Make sure Firebase project is active
4. Try logging out and back in to admin panel

## ✅ Success Checklist
- [ ] Firebase project created
- [ ] Firestore database enabled
- [ ] Authentication enabled with Email/Password
- [ ] Admin user created
- [ ] Security rules updated and published
- [ ] Certificate creation works

Once everything works, you can switch to production security rules for better security!
