import React, { useState, useEffect, useRef, useCallback } from 'react';
import { X, Calendar, Users, MessageCircle, Instagram, ExternalLink, Shield, Zap, Target, ChevronUp, ChevronDown } from 'lucide-react';

interface TrainingPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

const TrainingPopup: React.FC<TrainingPopupProps> = ({ isOpen, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showScrollBottom, setShowScrollBottom] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      document.body.style.overflow = 'hidden';
      setTimeout(checkScrollState, 100);
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const checkScrollState = () => {
    if (scrollContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
      setIsScrolled(scrollTop > 20);
      setShowScrollTop(scrollTop > 100);
      setShowScrollBottom(scrollTop < scrollHeight - clientHeight - 100);
    }
  };

  const handleScroll = () => {
    checkScrollState();
  };

  const scrollToTop = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  const scrollToBottom = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        top: scrollContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const handleEscapeKey = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClose();
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => document.removeEventListener('keydown', handleEscapeKey);
    }
  }, [isOpen, handleEscapeKey]);

  if (!isOpen) return null;

  return (
    <div 
      className={`fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4 md:p-6 transition-all duration-300 ${isVisible ? 'bg-black/70 backdrop-blur-sm' : 'bg-black/0'}`}
      onClick={handleBackdropClick}
    >
      <div className={`relative w-full max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl max-h-[95vh] transition-all duration-300 transform ${isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}`}>
        
        {/* Enhanced Close Button */}
        <div className="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 md:-top-4 md:-right-4 z-20 flex flex-col space-y-2">
          <button
            onClick={handleClose}
            className="group w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 focus:ring-offset-black"
            title="Close (Press Esc)"
          >
            <X className="h-4 w-4 sm:h-4 sm:w-4 md:h-5 md:w-5 text-white group-hover:rotate-90 transition-transform duration-300" />
          </button>
          
          {/* Scroll Indicators */}
          {showScrollTop && (
            <button
              onClick={scrollToTop}
              className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 bg-psyco-green-DEFAULT hover:bg-psyco-green-dark rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-psyco-green-DEFAULT focus:ring-offset-2 focus:ring-offset-black"
              title="Scroll to top"
            >
              <ChevronUp className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
            </button>
          )}
          
          {showScrollBottom && (
            <button
              onClick={scrollToBottom}
              className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 bg-psyco-green-DEFAULT hover:bg-psyco-green-dark rounded-full flex items-center justify-center transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 focus:outline-none focus:ring-2 focus:ring-psyco-green-DEFAULT focus:ring-offset-2 focus:ring-offset-black"
              title="Scroll to bottom"
            >
              <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
            </button>
          )}
        </div>

        {/* Main Card with Scrollable Content */}
        <div className="glassmorphism rounded-xl sm:rounded-2xl overflow-hidden shadow-2xl flex flex-col max-h-full">
          
          {/* Fixed Header Section */}
          <div className={`bg-gradient-to-r from-psyco-green-DEFAULT to-psyco-green-dark p-4 sm:p-5 md:p-6 text-center relative overflow-hidden transition-all duration-300 ${isScrolled ? 'shadow-lg' : ''}`}>
            <div className="absolute inset-0 bg-gradient-to-r from-psyco-green-DEFAULT/20 to-psyco-green-dark/20"></div>
            <div className="relative z-10">
              <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                <Shield className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 text-white" />
              </div>
              <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-white mb-2 leading-tight">
                Join The Cyber Wolf Free Training Course
              </h2>
              <p className="text-white/90 text-sm sm:text-base md:text-lg leading-relaxed">
                Monthly Community Learning for Ethical Hacking
              </p>
            </div>
          </div>

          {/* Scrollable Content Container */}
          <div 
            ref={scrollContainerRef}
            className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-psyco-green-DEFAULT scrollbar-track-psyco-black-light"
            onScroll={handleScroll}
            style={{ maxHeight: 'calc(95vh - 200px)' }}
          >
            {/* Content Grid */}
            <div className="p-3 sm:p-4 md:p-5 lg:p-6 bg-psyco-black-light">
              <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-5 lg:gap-6">
                
                {/* Card 1: Monthly Training */}
                <div className="glassmorphism p-4 sm:p-5 md:p-6 rounded-lg sm:rounded-xl hover:transform hover:scale-105 transition-all duration-300">
                  <div className="text-center">
                    <div className="w-10 h-10 sm:w-11 sm:h-11 md:w-12 md:h-12 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                      <Calendar className="h-5 w-5 sm:h-5 sm:w-5 md:h-6 md:w-6 text-white" />
                    </div>
                    <h3 className="text-lg sm:text-lg md:text-xl font-bold text-white mb-2 sm:mb-3">Monthly Sessions</h3>
                    <p className="text-gray-300 text-xs sm:text-sm mb-3 sm:mb-4 leading-relaxed">
                      Join our monthly live training sessions covering advanced penetration testing, ethical hacking techniques, and cybersecurity fundamentals.
                    </p>
                    <div className="space-y-1 sm:space-y-2 text-xs text-gray-400">
                      <div className="flex items-center justify-center">
                        <Zap className="h-3 w-3 mr-1 text-psyco-green-DEFAULT flex-shrink-0" />
                        <span className="text-center">Live Interactive Sessions</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <Target className="h-3 w-3 mr-1 text-psyco-green-DEFAULT flex-shrink-0" />
                        <span className="text-center">Hands-on Practical Labs</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Card 2: Community Learning */}
                <div className="glassmorphism p-4 sm:p-5 md:p-6 rounded-lg sm:rounded-xl hover:transform hover:scale-105 transition-all duration-300">
                  <div className="text-center">
                    <div className="w-10 h-10 sm:w-11 sm:h-11 md:w-12 md:h-12 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                      <Users className="h-5 w-5 sm:h-5 sm:w-5 md:h-6 md:w-6 text-white" />
                    </div>
                    <h3 className="text-lg sm:text-lg md:text-xl font-bold text-white mb-2 sm:mb-3">Community Learning</h3>
                    <p className="text-gray-300 text-xs sm:text-sm mb-3 sm:mb-4 leading-relaxed">
                      Connect with fellow ethical hackers, share knowledge, participate in CTF challenges, and grow together in our supportive community.
                    </p>
                    <div className="space-y-1 sm:space-y-2 text-xs text-gray-400">
                      <div className="flex items-center justify-center">
                        <Users className="h-3 w-3 mr-1 text-psyco-green-DEFAULT flex-shrink-0" />
                        <span className="text-center">Active Community Support</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <Shield className="h-3 w-3 mr-1 text-psyco-green-DEFAULT flex-shrink-0" />
                        <span className="text-center">CTF Challenges & Competitions</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Card 3: Expert Guidance */}
                <div className="glassmorphism p-4 sm:p-5 md:p-6 rounded-lg sm:rounded-xl hover:transform hover:scale-105 transition-all duration-300">
                  <div className="text-center">
                    <div className="w-10 h-10 sm:w-11 sm:h-11 md:w-12 md:h-12 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                      <Shield className="h-5 w-5 sm:h-5 sm:w-5 md:h-6 md:w-6 text-white" />
                    </div>
                    <h3 className="text-lg sm:text-lg md:text-xl font-bold text-white mb-2 sm:mb-3">Expert Guidance</h3>
                    <p className="text-gray-300 text-xs sm:text-sm mb-3 sm:mb-4 leading-relaxed">
                      Learn from industry experts with real-world experience in cybersecurity, penetration testing, and ethical hacking methodologies.
                    </p>
                    <div className="space-y-1 sm:space-y-2 text-xs text-gray-400">
                      <div className="flex items-center justify-center">
                        <Target className="h-3 w-3 mr-1 text-psyco-green-DEFAULT flex-shrink-0" />
                        <span className="text-center">Industry Expert Instructors</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <Zap className="h-3 w-3 mr-1 text-psyco-green-DEFAULT flex-shrink-0" />
                        <span className="text-center">Real-world Case Studies</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Social Media & Action Section */}
              <div className="mt-6 sm:mt-7 md:mt-8 text-center">
                <h3 className="text-lg sm:text-xl font-bold text-white mb-3 sm:mb-4">Join Our Community</h3>
                
                {/* Social Media Links */}
                <div className="flex justify-center space-x-4 sm:space-x-5 md:space-x-6 mb-5 sm:mb-6">
                  {/* WhatsApp */}
                  <a
                    href="https://whatsapp.com/channel/0029VbAmBt7GufIqh9iwND05"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex flex-col items-center"
                  >
                    <div className="w-12 h-12 sm:w-13 sm:h-13 md:w-14 md:h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group-hover:from-green-400 group-hover:to-green-500">
                      <MessageCircle className="h-6 w-6 sm:h-6 sm:w-6 md:h-7 md:w-7 text-white" />
                    </div>
                    <span className="text-gray-300 text-xs sm:text-sm mt-1 sm:mt-2 group-hover:text-white transition-colors">WhatsApp</span>
                  </a>

                  {/* Instagram */}
                  <a
                    href="https://www.instagram.com/p/DL7imlFvUHk/?igsh=MXJjbG9scng0dnZrZA=="
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group flex flex-col items-center"
                  >
                    <div className="w-12 h-12 sm:w-13 sm:h-13 md:w-14 md:h-14 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group-hover:from-pink-400 group-hover:to-purple-500">
                      <Instagram className="h-6 w-6 sm:h-6 sm:w-6 md:h-7 md:w-7 text-white" />
                    </div>
                    <span className="text-gray-300 text-xs sm:text-sm mt-1 sm:mt-2 group-hover:text-white transition-colors">Instagram</span>
                  </a>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
                  <a
                    href="/registration"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-2.5 sm:py-3 px-6 sm:px-8 rounded-lg transition-all duration-300 btn-glow text-sm sm:text-base"
                  >
                    <Users className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                    Join Free Training
                  </a>
                  <a
                    href="https://cyberwolf-career-guidance.web.app/details.html"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center bg-transparent border border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10 font-medium py-2.5 sm:py-3 px-6 sm:px-8 rounded-lg transition-all duration-300 text-sm sm:text-base"
                  >
                    <ExternalLink className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                    Learn More
                  </a>
                </div>

                {/* Additional Info */}
                <div className="mt-5 sm:mt-6 p-3 sm:p-4 bg-psyco-green-DEFAULT/10 rounded-lg border border-psyco-green-DEFAULT/30">
                  <div className="text-gray-300 text-xs sm:text-sm space-y-1 sm:space-y-0 sm:space-x-2">
                    <div className="block sm:inline">
                      <strong className="text-white">Next Session:</strong> Every month
                    </div>
                    <span className="hidden sm:inline">•</span>
                    <div className="block sm:inline">
                      <strong className="text-white">Duration:</strong> 2-3 hours
                    </div>
                    <span className="hidden sm:inline">•</span>
                    <div className="block sm:inline">
                      <strong className="text-white">Cost:</strong> Completely FREE
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrainingPopup;
