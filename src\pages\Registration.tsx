import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Mail, Phone, User, GraduationCap, School, Calendar, MapPin, Clock, CheckCircle, Award, Shield, MessageCircle, Users, Camera, ExternalLink } from "lucide-react";
import emailjs from '@emailjs/browser';
import cyberwolf<PERSON>ogo from "@/assets/cyberwolf-logo.png";
import speaker1 from "@/assets/speaker1.png";
import speaker2 from "@/assets/speaker2.png";

const formSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  collegeName: z.string().optional(),
  degree: z.string().optional(),
  school: z.string().min(2, "School name is required"),
  age: z.string().min(1, "Age is required").refine((val) => {
    const age = parseInt(val);
    return age >= 16 && age <= 50;
  }, "Age must be between 16 and 50"),
  contactNumber: z.string().min(10, "Contact number must be at least 10 digits").regex(/^[+]?[0-9\s-()]+$/, "Please enter a valid phone number"),
  email: z.string().email("Please enter a valid email address"),
  experience: z.string().optional(),
  motivation: z.string().optional(),
  referralSource: z.string().optional(),
  // Mandatory Social Media Following
  whatsappJoined: z.boolean().refine((val) => val === true, "You must join our WhatsApp community to proceed"),
  linkedinFollowed: z.boolean().refine((val) => val === true, "You must follow us on LinkedIn to proceed"),
  instagramFollowed: z.boolean().refine((val) => val === true, "You must follow us on Instagram to proceed"),
  // Certificate Information
  certificateDelivery: z.enum(["email", "whatsapp"], { required_error: "Please select certificate delivery method" }),
  agreeToCommunity: z.boolean().refine((val) => val === true, "You must agree to community guidelines"),
});

type FormData = z.infer<typeof formSchema>;

const Registration = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);
  const { toast } = useToast();

  // Initialize EmailJS
  useEffect(() => {
    emailjs.init("UReJUOuf7nicAZWRS"); // EmailJS public key
  }, []);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      collegeName: "",
      degree: "",
      school: "",
      age: "",
      contactNumber: "",
      email: "",
      experience: "",
      motivation: "",
      referralSource: "",
      whatsappJoined: false,
      linkedinFollowed: false,
      instagramFollowed: false,
      certificateDelivery: undefined,
      agreeToCommunity: false,
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);

    try {
      // Prepare EmailJS template parameters
      const templateParams = {
        to_email: '<EMAIL>',
        from_name: data.name,
        from_email: data.email,
        subject: `Course Registration - ${data.name} - Web Penetration Testing`,
        student_name: data.name,
        student_age: data.age,
        student_email: data.email,
        student_phone: data.contactNumber,
        student_whatsapp: data.contactNumber,
        student_school: data.school,
        student_college: data.collegeName || "Not provided",
        student_degree: data.degree || "Not provided",
        student_experience: data.experience || "Not provided",
        student_motivation: data.motivation || "Not provided",
        student_referral: data.referralSource || "Not provided",
        // Social Media Following Status
        whatsapp_joined: data.whatsappJoined ? "✅ Joined" : "❌ Not Joined",
        linkedin_followed: data.linkedinFollowed ? "✅ Following" : "❌ Not Following",
        instagram_followed: data.instagramFollowed ? "✅ Following" : "❌ Not Following",
        // Certificate Information
        certificate_delivery: data.certificateDelivery,
        community_agreement: data.agreeToCommunity ? "✅ Agreed" : "❌ Not Agreed",
        course_name: "FREE WEB PENETRATION TESTING COURSE",
        course_date: "18th July 2025 (Friday)",
        course_time: "11:00 AM – 3:00 PM (IST)",
        course_mode: "Online",
        registration_time: new Date().toLocaleString(),
        message: `
NEW COURSE REGISTRATION - WEB PENETRATION TESTING COURSE

======================================
PERSONAL INFORMATION
======================================
Name: ${data.name}
Age: ${data.age}
Email: ${data.email}
WhatsApp Number: ${data.contactNumber}

======================================
EDUCATIONAL BACKGROUND
======================================
School: ${data.school}
College: ${data.collegeName || "Not provided"}
Degree: ${data.degree || "Not provided"}

======================================
ADDITIONAL INFORMATION
======================================
Previous Experience: ${data.experience || "Not provided"}
Motivation: ${data.motivation || "Not provided"}
How did you hear about us: ${data.referralSource || "Not provided"}

======================================
SOCIAL MEDIA FOLLOWING STATUS
======================================
WhatsApp Community: ${data.whatsappJoined ? "✅ Joined" : "❌ Not Joined"}
LinkedIn Following: ${data.linkedinFollowed ? "✅ Following" : "❌ Not Following"}
Instagram Following: ${data.instagramFollowed ? "✅ Following" : "❌ Not Following"}

======================================
CERTIFICATE & COMMUNITY
======================================
Certificate Delivery Method: ${data.certificateDelivery}
Community Guidelines Agreement: ${data.agreeToCommunity ? "✅ Agreed" : "❌ Not Agreed"}

======================================
COURSE DETAILS
======================================
Course: FREE WEB PENETRATION TESTING COURSE
Date: 18th July 2025 (Friday)
Time: 11:00 AM – 3:00 PM (IST)
Mode: Online

Registration Time: ${new Date().toLocaleString()}
        `
      };

      // Send email using EmailJS
      const result = await emailjs.send(
        'service_05rrk3a', // EmailJS service ID
        'template_1m7xtza', // EmailJS template ID
        templateParams
      );

      console.log('Email sent successfully:', result);

      // Show success state
      setIsRegistered(true);

      toast({
        title: "Registration Successful!",
        description: "Your registration has been submitted successfully. We'll contact you soon with course details.",
      });

      // Auto scroll to top
      window.scrollTo({ top: 0, behavior: 'smooth' });

    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isRegistered) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT flex items-center justify-center px-3 sm:px-4 md:px-6">
        <Card className="w-full max-w-lg mx-auto glassmorphism text-center">
          <CardContent className="pt-6 sm:pt-8 pb-6 sm:pb-8 px-4 sm:px-6">
            <div className="mb-4 sm:mb-6">
              <CheckCircle className="mx-auto text-psyco-green-DEFAULT mb-3 sm:mb-4" size={48} />
              <h2 className="text-xl sm:text-2xl font-bold text-white mb-2">Registration Successful!</h2>
              <p className="text-gray-300 mb-3 sm:mb-4 text-sm sm:text-base px-2">
                Your registration email has been prepared and opened in your default email client.
              </p>
              <div className="bg-psyco-green-DEFAULT/10 border border-psyco-green-DEFAULT/30 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
                <p className="text-white text-xs sm:text-sm text-left">
                  <strong>Next Steps:</strong><br />
                  1. Check your email client<br />
                  2. Send the pre-filled email<br />
                  3. Wait for confirmation
                </p>
              </div>
              <Button 
                onClick={() => setIsRegistered(false)}
                variant="outline"
                className="border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT hover:text-white"
              >
                Register Another Person
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT py-4 sm:py-6 md:py-8 lg:py-12 px-3 sm:px-4 md:px-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6 sm:mb-8 md:mb-10 lg:mb-12">
          <div className="flex justify-center mb-4 sm:mb-6">
            <img
              src={cyberwolfLogo}
              alt="Cyber Wolf Logo"
              className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 object-contain animate-fade-in"
            />
          </div>
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-3 sm:mb-4 animate-fade-in px-2">
            Course Registration
          </h1>
          <p className="text-psyco-green-DEFAULT text-base sm:text-lg md:text-xl lg:text-2xl font-medium mb-6 sm:mb-8 animate-fade-in px-2">
            FREE WEB PENETRATION TESTING COURSE
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 items-start">
          {/* Course Information */}
          <div className="space-y-4 sm:space-y-6 order-2 lg:order-1">
            {/* Course Details Card */}
            <Card className="glassmorphism">
              <CardHeader className="pb-3 sm:pb-4">
                <CardTitle className="text-white flex items-center gap-2 sm:gap-3 text-lg sm:text-xl">
                  <Shield className="text-psyco-green-DEFAULT" size={20} />
                  Course Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 sm:space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-sm">
                  <div className="flex items-center gap-2 sm:gap-3 text-gray-300">
                    <Calendar className="text-psyco-green-DEFAULT flex-shrink-0" size={16} />
                    <span className="text-xs sm:text-sm">18th July 2025 (Friday)</span>
                  </div>
                  <div className="flex items-center gap-2 sm:gap-3 text-gray-300">
                    <Clock className="text-psyco-green-DEFAULT flex-shrink-0" size={16} />
                    <span className="text-xs sm:text-sm">11:00 AM – 3:00 PM (IST)</span>
                  </div>
                  <div className="flex items-center gap-2 sm:gap-3 text-gray-300">
                    <MapPin className="text-psyco-green-DEFAULT flex-shrink-0" size={16} />
                    <span className="text-xs sm:text-sm">Online Mode</span>
                  </div>
                  <div className="flex items-center gap-2 sm:gap-3 text-gray-300">
                    <Award className="text-psyco-green-DEFAULT flex-shrink-0" size={16} />
                    <span className="text-xs sm:text-sm">Free Certificate</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Topics Covered */}
            <Card className="glassmorphism">
              <CardHeader>
                <CardTitle className="text-white">What You'll Learn</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 text-gray-300">
                  {[
                    "Web Penetration Testing Fundamentals",
                    "OWASP Top 10 Vulnerabilities",
                    "Real-time Hacking Demonstrations",
                    "Industry Tools: Burp Suite, Nmap, Nikto",
                    "Ethical Hacking Methodologies",
                    "Security Assessment Techniques"
                  ].map((topic, index) => (
                    <li key={index} className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-psyco-green-DEFAULT rounded-full"></div>
                      {topic}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Speakers */}
            <Card className="glassmorphism">
              <CardHeader>
                <CardTitle className="text-white text-center">Course Instructors</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 text-center mb-6">Cyber Wolf Security Research Team</p>
                <div className="flex justify-center gap-8">
                  <div className="text-center">
                    <img 
                      src={speaker1} 
                      alt="Senior Security Researcher" 
                      className="w-20 h-20 rounded-full object-cover mx-auto mb-3 border-2 border-psyco-green-DEFAULT"
                    />
                    <p className="text-white text-sm font-medium">Security Expert</p>
                    <p className="text-gray-400 text-xs">Lead Researcher</p>
                  </div>
                  <div className="text-center">
                    <img 
                      src={speaker2} 
                      alt="Penetration Testing Specialist" 
                      className="w-20 h-20 rounded-full object-cover mx-auto mb-3 border-2 border-psyco-green-DEFAULT"
                    />
                    <p className="text-white text-sm font-medium">Penetration Tester</p>
                    <p className="text-gray-400 text-xs">Security Specialist</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="glassmorphism">
              <CardHeader>
                <CardTitle className="text-white">Contact Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-gray-300">
                  <div className="flex items-center gap-3">
                    <Mail className="text-psyco-green-DEFAULT" size={16} />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="text-psyco-green-DEFAULT" size={16} />
                    <span>+91 6374 344 424</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="text-psyco-green-DEFAULT" size={16} />
                    <span>+91 6379 869 678</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Registration Form */}
          <div className="lg:sticky lg:top-6 order-1 lg:order-2">
            <Card className="glassmorphism">
              <CardHeader className="text-center pb-4 sm:pb-6">
                <CardTitle className="text-xl sm:text-2xl font-bold text-white mb-2">Register Now</CardTitle>
                <CardDescription className="text-gray-300 text-sm sm:text-base px-2">
                  Fill out the form below to secure your spot
                </CardDescription>
              </CardHeader>
              <CardContent className="px-3 sm:px-6">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3 sm:space-y-4">
                    {/* Personal Information */}
                    <div className="space-y-3 sm:space-y-4">
                      <h3 className="text-white font-semibold border-b border-psyco-green-DEFAULT/30 pb-2 text-sm sm:text-base">
                        👤 Personal Information
                      </h3>
                      
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white flex items-center gap-2 text-sm sm:text-base">
                              <User size={14} className="sm:w-4 sm:h-4" />
                              Full Name *
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter your full name"
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 h-10 sm:h-11 text-sm sm:text-base"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="age"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white flex items-center gap-2">
                                <Calendar size={16} />
                                Age *
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="Age"
                                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="contactNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white flex items-center gap-2">
                                <Phone size={16} />
                                Whatsapp Number *
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="tel"
                                  placeholder="Phone number"
                                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white flex items-center gap-2">
                              <Mail size={16} />
                              Email Address *
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="Enter your email address"
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Educational Background */}
                    <div className="space-y-4">
                      <h3 className="text-white font-semibold border-b border-psyco-green-DEFAULT/30 pb-2">
                        Educational Background
                      </h3>
                      
                      <FormField
                        control={form.control}
                        name="school"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white flex items-center gap-2">
                              <School size={16} />
                              School/Institution *
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter your school/institution name"
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="collegeName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white flex items-center gap-2">
                                <GraduationCap size={16} />
                                College (Optional)
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="College name"
                                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="degree"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-white">Degree (Optional)</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger className="bg-white/10 border-white/20 text-white">
                                    <SelectValue placeholder="Select degree" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="be">BE (Bachelor of Engineering)</SelectItem>
                                  <SelectItem value="btech">B.Tech (Bachelor of Technology)</SelectItem>
                                  <SelectItem value="bca">BCA (Bachelor of Computer Applications)</SelectItem>
                                  <SelectItem value="bsc">B.Sc Computer Science</SelectItem>
                                  <SelectItem value="me">ME (Master of Engineering)</SelectItem>
                                  <SelectItem value="mtech">M.Tech (Master of Technology)</SelectItem>
                                  <SelectItem value="mca">MCA (Master of Computer Applications)</SelectItem>
                                  <SelectItem value="msc">M.Sc Computer Science</SelectItem>
                                  <SelectItem value="diploma">Diploma in Engineering</SelectItem>
                                  <SelectItem value="other">Other</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Additional Information */}
                    <div className="space-y-4">
                      <h3 className="text-white font-semibold border-b border-psyco-green-DEFAULT/30 pb-2">
                        Additional Information (Optional)
                      </h3>
                      
                      <FormField
                        control={form.control}
                        name="experience"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Previous Experience in Cybersecurity</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Tell us about any previous experience in cybersecurity, programming, or related fields..."
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 resize-none"
                                rows={3}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="motivation"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">Why are you interested in this course?</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Share your motivation and what you hope to learn..."
                                className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 resize-none"
                                rows={3}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="referralSource"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white">How did you hear about us?</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="bg-white/10 border-white/20 text-white">
                                  <SelectValue placeholder="Select source" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="social-media">Social Media</SelectItem>
                                <SelectItem value="friend-referral">Friend/Colleague Referral</SelectItem>
                                <SelectItem value="college">College/University</SelectItem>
                                <SelectItem value="search-engine">Search Engine</SelectItem>
                                <SelectItem value="cybersecurity-community">Cybersecurity Community</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Mandatory Social Media Following */}
                    <div className="space-y-3 sm:space-y-4">
                      <div className="bg-gradient-to-r from-psyco-green-DEFAULT/20 to-psyco-green-dark/20 p-3 sm:p-4 rounded-lg border border-psyco-green-DEFAULT/30">
                        <h3 className="text-white font-semibold flex items-center gap-2 mb-2 text-sm sm:text-base">
                          <Shield className="h-4 w-4 sm:h-5 sm:w-5 text-psyco-green-DEFAULT flex-shrink-0" />
                          <span className="leading-tight">Mandatory Social Media Following (Required for Certificate)</span>
                        </h3>
                        <p className="text-gray-300 text-xs sm:text-sm leading-relaxed">
                          To receive your completion certificate and stay updated with our community, you must follow us on all platforms below:
                        </p>
                        <div className="flex items-center gap-2 mt-2 text-xs text-psyco-green-DEFAULT">
                          <Award className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                          <span className="leading-tight">Certificate will only be issued to active community members</span>
                        </div>
                      </div>

                      {/* WhatsApp Community */}
                      <FormField
                        control={form.control}
                        name="whatsappJoined"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-lg border-2 border-green-500/30 bg-green-500/5 p-4 hover:border-green-500/50 transition-all duration-300">
                            <FormControl>
                              <input
                                type="checkbox"
                                checked={field.value}
                                onChange={field.onChange}
                                className="w-4 h-4 text-psyco-green-DEFAULT bg-white/10 border-white/20 rounded focus:ring-psyco-green-DEFAULT focus:ring-2"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none flex-1">
                              <FormLabel className="text-white font-medium flex items-center gap-2">
                                <MessageCircle className="h-4 w-4 text-green-500" />
                                Join WhatsApp Community *
                              </FormLabel>
                              <p className="text-gray-400 text-sm mb-3">
                                Join our active WhatsApp community for course updates, discussions, and networking.
                              </p>
                              <a
                                href="https://whatsapp.com/channel/0029VbAmBt7GufIqh9iwND05"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center bg-green-500 hover:bg-green-600 text-white font-medium py-2 sm:py-2.5 px-3 sm:px-4 rounded-lg transition-all duration-300 text-xs sm:text-sm shadow-lg hover:shadow-xl w-full sm:w-auto justify-center"
                              >
                                <MessageCircle className="mr-1.5 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                <span className="truncate">Join WhatsApp Community</span>
                                <ExternalLink className="ml-1.5 sm:ml-2 h-2.5 w-2.5 sm:h-3 sm:w-3 flex-shrink-0" />
                              </a>
                              <p className="text-green-400 text-xs mt-2 flex items-center">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Click the button above, then check the box to confirm you've joined
                              </p>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* LinkedIn Following */}
                      <FormField
                        control={form.control}
                        name="linkedinFollowed"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-lg border-2 border-blue-500/30 bg-blue-500/5 p-4 hover:border-blue-500/50 transition-all duration-300">
                            <FormControl>
                              <input
                                type="checkbox"
                                checked={field.value}
                                onChange={field.onChange}
                                className="w-4 h-4 text-psyco-green-DEFAULT bg-white/10 border-white/20 rounded focus:ring-psyco-green-DEFAULT focus:ring-2"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none flex-1">
                              <FormLabel className="text-white font-medium flex items-center gap-2">
                                <Users className="h-4 w-4 text-blue-500" />
                                Follow on LinkedIn *
                              </FormLabel>
                              <p className="text-gray-400 text-sm mb-3">
                                Follow our LinkedIn page for professional updates and career opportunities.
                              </p>
                              <a
                                href="https://www.linkedin.com/company/cyberwolf-team/"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 sm:py-2.5 px-3 sm:px-4 rounded-lg transition-all duration-300 text-xs sm:text-sm shadow-lg hover:shadow-xl w-full sm:w-auto justify-center"
                              >
                                <Users className="mr-1.5 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                <span className="truncate">Follow on LinkedIn</span>
                                <ExternalLink className="ml-1.5 sm:ml-2 h-2.5 w-2.5 sm:h-3 sm:w-3 flex-shrink-0" />
                              </a>
                              <p className="text-blue-400 text-xs mt-2 flex items-center">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Click the button above, then check the box to confirm you're following
                              </p>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Instagram Following */}
                      <FormField
                        control={form.control}
                        name="instagramFollowed"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-lg border-2 border-pink-500/30 bg-pink-500/5 p-4 hover:border-pink-500/50 transition-all duration-300">
                            <FormControl>
                              <input
                                type="checkbox"
                                checked={field.value}
                                onChange={field.onChange}
                                className="w-4 h-4 text-psyco-green-DEFAULT bg-white/10 border-white/20 rounded focus:ring-psyco-green-DEFAULT focus:ring-2"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none flex-1">
                              <FormLabel className="text-white font-medium flex items-center gap-2">
                                <Camera className="h-4 w-4 text-pink-500" />
                                Follow on Instagram *
                              </FormLabel>
                              <p className="text-gray-400 text-sm mb-3">
                                Follow our Instagram for behind-the-scenes content and cybersecurity tips.
                              </p>
                              <a
                                href="https://www.instagram.com/p/DL7imlFvUHk/?igsh=MXJjbG9scng0dnZrZA=="
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-medium py-2 sm:py-2.5 px-3 sm:px-4 rounded-lg transition-all duration-300 text-xs sm:text-sm shadow-lg hover:shadow-xl w-full sm:w-auto justify-center"
                              >
                                <Camera className="mr-1.5 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                                <span className="truncate">Follow on Instagram</span>
                                <ExternalLink className="ml-1.5 sm:ml-2 h-2.5 w-2.5 sm:h-3 sm:w-3 flex-shrink-0" />
                              </a>
                              <p className="text-pink-400 text-xs mt-2 flex items-center">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Click the button above, then check the box to confirm you're following
                              </p>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Certificate Delivery & Community Agreement */}
                    <div className="space-y-4">
                      <h3 className="text-white font-semibold border-b border-psyco-green-DEFAULT/30 pb-2">
                        🏆 Certificate Delivery & Community Guidelines
                      </h3>

                      {/* Certificate Delivery Method */}
                      <FormField
                        control={form.control}
                        name="certificateDelivery"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-white flex items-center gap-2">
                              📜 Certificate Delivery Method *
                            </FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="bg-white/10 border-white/20 text-white">
                                  <SelectValue placeholder="Select delivery method" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="email">📧 Email (PDF Certificate)</SelectItem>
                                <SelectItem value="whatsapp">📱 WhatsApp (Digital Certificate)</SelectItem>
                              </SelectContent>
                            </Select>
                            <p className="text-gray-400 text-xs">
                              Choose how you'd like to receive your completion certificate
                            </p>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Community Guidelines Agreement */}
                      <FormField
                        control={form.control}
                        name="agreeToCommunity"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border border-white/20 p-4">
                            <FormControl>
                              <input
                                type="checkbox"
                                checked={field.value}
                                onChange={field.onChange}
                                className="w-4 h-4 text-psyco-green-DEFAULT bg-white/10 border-white/20 rounded focus:ring-psyco-green-DEFAULT focus:ring-2"
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none flex-1">
                              <FormLabel className="text-white font-medium">
                                📋 Community Guidelines Agreement *
                              </FormLabel>
                              <p className="text-gray-400 text-sm">
                                I agree to follow the Cyber Wolf community guidelines, maintain respectful communication,
                                and use the knowledge gained for ethical purposes only. I understand that certificates
                                will only be issued to active community members who complete the course requirements.
                              </p>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-3 sm:py-4 text-sm sm:text-base md:text-lg btn-glow min-h-[44px] sm:min-h-[48px]"
                      disabled={isSubmitting}
                    >
                      <span className="truncate">
                        {isSubmitting ? "Processing Registration..." : "🏆 Complete Registration & Join Community"}
                      </span>
                    </Button>

                    <div className="space-y-2 text-xs text-gray-400 text-center">
                      <p>
                        By registering, you agree to receive course-related communications from Cyber Wolf.
                      </p>
                      <p className="text-psyco-green-DEFAULT">
                        📜 <strong>Certificate Note:</strong> Completion certificates will only be issued to students who:
                        ✅ Complete the course, ✅ Follow all social media accounts, ✅ Remain active in the community
                      </p>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Registration;