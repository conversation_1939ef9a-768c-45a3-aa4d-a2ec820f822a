# EmailJS Setup Guide for Cyber Wolf Registration

## Overview
This guide will help you set up EmailJS to handle course registration emails automatically.

## Step 1: Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

## Step 2: Create Email Service
1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail, Outlook, etc.)
4. Follow the setup instructions for your provider
5. Note down your **Service ID** (e.g., `service_abc123`)

## Step 3: Create Email Template
1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Use the following template structure:

### Template Variables to Include:
```
{{to_email}} - Recipient email
{{from_name}} - Student name
{{from_email}} - Student email
{{subject}} - Email subject
{{student_name}} - Student full name
{{student_age}} - Student age
{{student_email}} - Student email
{{student_phone}} - Student phone number
{{student_school}} - Student school
{{student_college}} - Student college
{{student_degree}} - Student degree
{{student_experience}} - Previous experience
{{student_motivation}} - Motivation for joining
{{student_referral}} - How they heard about us
{{course_name}} - Course name
{{course_date}} - Course date
{{course_time}} - Course time
{{course_mode}} - Course mode (Online/Offline)
{{registration_time}} - Registration timestamp
{{message}} - Full formatted message
```

### Template Content:
You can either:
- **Option A**: Use the HTML template from `public/emailjs_template.html`
- **Option B**: Use a simple text template with `{{message}}` variable

### Simple Text Template:
```
Subject: New Course Registration - {{student_name}}

{{message}}

---
This registration was submitted on {{registration_time}}
```

## Step 4: Get Your Keys
1. Go to "Account" → "General"
2. Note down your **Public Key** (e.g., `user_abc123def456`)
3. Note down your **Template ID** from the template you created

## Step 5: Update the Code
Replace the placeholder values in `src/pages/Registration.tsx`:

```typescript
// Replace these with your actual EmailJS credentials
emailjs.init("YOUR_PUBLIC_KEY"); // Your public key
const result = await emailjs.send(
  'YOUR_SERVICE_ID',    // Your service ID
  'YOUR_TEMPLATE_ID',   // Your template ID
  templateParams
);
```

## Step 6: Install EmailJS Package
Run this command in your project directory:
```bash
npm install @emailjs/browser
```

## Step 7: Test the Integration
1. Fill out the registration form
2. Submit the form
3. Check your email inbox for the registration email
4. Verify all template variables are populated correctly

## Security Notes
- Never expose your private key in client-side code
- Use EmailJS public key only (it's safe for client-side use)
- Consider setting up email filters/rules for registration emails
- Monitor your EmailJS usage to stay within free tier limits

## Troubleshooting

### Common Issues:
1. **Email not sending**: Check service configuration and credentials
2. **Template variables not working**: Ensure variable names match exactly
3. **CORS errors**: Make sure your domain is added to EmailJS allowed origins
4. **Rate limiting**: EmailJS free tier has sending limits

### Debug Steps:
1. Check browser console for errors
2. Verify EmailJS service status
3. Test with a simple template first
4. Check EmailJS dashboard for sending logs

## Free Tier Limits
- 200 emails per month
- 2 email services
- 1 email template
- Basic support

## Upgrade Options
Consider upgrading if you need:
- More emails per month
- Multiple templates
- Priority support
- Advanced features

## Support
- EmailJS Documentation: https://www.emailjs.com/docs/
- EmailJS Support: <EMAIL>
- Project Issues: Contact development team
