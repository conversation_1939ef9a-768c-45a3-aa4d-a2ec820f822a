import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Shield, 
  Award, 
  Edit, 
  Trash2, 
  Eye, 
  Search, 
  Calendar, 
  User, 
  GraduationCap, 
  MapPin,
  Save,
  X,
  Plus,
  Download,
  Filter,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { collection, getDocs, doc, updateDoc, deleteDoc, query, orderBy, where } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface CertificateData {
  id: string;
  certificateId: string;
  name: string;
  age: number;
  courseName: string;
  college: string;
  country: string;
  state: string;
  courseStartDate?: string;
  courseJoiningDate?: string;
  courseEndDate: string;
  certificateProvideDate: string;
  createdAt: any;
  createdBy: string;
  status: string;
}

const AdminDashboard = () => {
  const [certificates, setCertificates] = useState<CertificateData[]>([]);
  const [filteredCertificates, setFilteredCertificates] = useState<CertificateData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<CertificateData>>({});
  const [viewingId, setViewingId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const { toast } = useToast();

  // Fetch all certificates
  const fetchCertificates = async () => {
    setIsLoading(true);
    try {
      if (!db) {
        throw new Error("Firebase not configured");
      }

      const q = query(collection(db, 'certificates'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      const certificatesData: CertificateData[] = [];
      querySnapshot.forEach((doc) => {
        certificatesData.push({
          id: doc.id,
          ...doc.data()
        } as CertificateData);
      });

      setCertificates(certificatesData);
      setFilteredCertificates(certificatesData);
      
      toast({
        title: "Data Loaded",
        description: `Found ${certificatesData.length} certificates`,
      });
    } catch (error: any) {
      console.error("Error fetching certificates:", error);
      toast({
        title: "Error",
        description: "Failed to load certificates. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Filter certificates
  useEffect(() => {
    let filtered = certificates;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(cert => 
        cert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.certificateId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.courseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cert.college.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(cert => cert.status === statusFilter);
    }

    setFilteredCertificates(filtered);
  }, [searchTerm, statusFilter, certificates]);

  // Load certificates on component mount
  useEffect(() => {
    fetchCertificates();
  }, []);

  // Start editing
  const startEdit = (certificate: CertificateData) => {
    setEditingId(certificate.id);
    setEditForm(certificate);
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingId(null);
    setEditForm({});
  };

  // Save changes
  const saveChanges = async () => {
    if (!editingId || !editForm) return;

    try {
      if (!db) {
        throw new Error("Firebase not configured");
      }

      const docRef = doc(db, 'certificates', editingId);
      await updateDoc(docRef, {
        ...editForm,
        updatedAt: new Date(),
        updatedBy: "admin"
      });

      // Update local state
      setCertificates(prev => 
        prev.map(cert => 
          cert.id === editingId ? { ...cert, ...editForm } : cert
        )
      );

      setEditingId(null);
      setEditForm({});

      toast({
        title: "Certificate Updated",
        description: "Certificate information has been successfully updated.",
      });
    } catch (error: any) {
      console.error("Error updating certificate:", error);
      toast({
        title: "Update Failed",
        description: "Failed to update certificate. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Delete certificate
  const deleteCertificate = async (id: string, certificateId: string) => {
    if (!confirm(`Are you sure you want to delete certificate ${certificateId}? This action cannot be undone.`)) {
      return;
    }

    setIsDeleting(id);
    try {
      if (!db) {
        throw new Error("Firebase not configured");
      }

      await deleteDoc(doc(db, 'certificates', id));

      // Update local state
      setCertificates(prev => prev.filter(cert => cert.id !== id));

      toast({
        title: "Certificate Deleted",
        description: `Certificate ${certificateId} has been permanently deleted.`,
      });
    } catch (error: any) {
      console.error("Error deleting certificate:", error);
      toast({
        title: "Delete Failed",
        description: "Failed to delete certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(null);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format timestamp
  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return "Unknown";
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
              <p className="text-gray-400">Certificate Management & History</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={fetchCertificates}
              variant="outline"
              className="border-psyco-green-DEFAULT/50 text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="glassmorphism">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Total Certificates</p>
                  <p className="text-2xl font-bold text-white">{certificates.length}</p>
                </div>
                <Award className="h-8 w-8 text-psyco-green-DEFAULT" />
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Active Certificates</p>
                  <p className="text-2xl font-bold text-white">
                    {certificates.filter(c => c.status === 'active').length}
                  </p>
                </div>
                <Shield className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">This Month</p>
                  <p className="text-2xl font-bold text-white">
                    {certificates.filter(c => {
                      const created = c.createdAt?.toDate ? c.createdAt.toDate() : new Date(c.createdAt);
                      const thisMonth = new Date();
                      return created.getMonth() === thisMonth.getMonth() && 
                             created.getFullYear() === thisMonth.getFullYear();
                    }).length}
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Filtered Results</p>
                  <p className="text-2xl font-bold text-white">{filteredCertificates.length}</p>
                </div>
                <Filter className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter Controls */}
        <Card className="glassmorphism mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Label htmlFor="search" className="text-white mb-2 block">Search Certificates</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    type="text"
                    placeholder="Search by name, certificate ID, course, or college..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  />
                </div>
              </div>
              <div className="md:w-48">
                <Label htmlFor="status" className="text-white mb-2 block">Filter by Status</Label>
                <select
                  id="status"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full p-2 rounded-md bg-white/10 border border-white/20 text-white"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="suspended">Suspended</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>
