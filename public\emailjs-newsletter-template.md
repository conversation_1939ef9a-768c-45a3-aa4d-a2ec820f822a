# EmailJS Newsletter Subscription Template Setup

## Template Configuration

### Service ID: `service_05rrk3a`
### Template ID: `template_9p1ynh2`
### Public Key: `UReJUOuf7nicAZWRS`

## Template Variables

Use these variables in your EmailJS template:

- `{{user_email}}` - Subscriber's email address
- `{{subscription_date}}` - Date of subscription
- `{{to_email}}` - Recipient email (same as user_email)
- `{{from_name}}` - Sender name (Cyber Wolf Team)
- `{{message}}` - Welcome message

## EmailJS Template HTML Structure

```html
<!DOCTYPE html>
<html>
<head>
    <title>Welcome to Cyber Wolf Newsletter</title>
</head>
<body>
    <h1>🐺 Welcome to Cyber Wolf Newsletter!</h1>
    
    <p>Hello {{user_email}},</p>
    
    <p>{{message}}</p>
    
    <p>Subscription Date: {{subscription_date}}</p>
    
    <h2>What you'll receive:</h2>
    <ul>
        <li>Latest cybersecurity articles and tutorials</li>
        <li>Advanced penetration testing techniques</li>
        <li>Security frameworks and compliance guides</li>
        <li>Exclusive CTF challenges and solutions</li>
        <li>Industry insights and threat intelligence</li>
        <li>Early access to new tools and resources</li>
    </ul>
    
    <p>Best regards,<br>
    {{from_name}}<br>
    Cyber Wolf Team</p>
</body>
</html>
```

## Setup Instructions

1. **Login to EmailJS Dashboard**
   - Go to https://www.emailjs.com/
   - Login with your account

2. **Create Email Service**
   - Add email service with ID: `service_05rrk3a`
   - Configure your email provider (Gmail, Outlook, etc.)

3. **Create Email Template**
   - Create new template with ID: `template_9p1ynh2`
   - Use the HTML structure above
   - Configure template variables

4. **Configure Public Key**
   - Use public key: `UReJUOuf7nicAZWRS`
   - Add domain to allowed origins

## Template Features

✅ **Responsive Design** - Works on all devices
✅ **Professional Styling** - Cyber Wolf branding
✅ **Dynamic Content** - Personalized with user data
✅ **Social Links** - Connect with community
✅ **Unsubscribe Option** - GDPR compliant
✅ **Security Focus** - Cybersecurity themed content

## Testing

Test the subscription with:
- Valid email addresses
- Invalid email formats
- Network connectivity issues
- EmailJS service limits

## Error Handling

The implementation includes:
- Email validation
- Loading states
- Success/error messages
- Automatic message reset
- Form validation

## Security Considerations

- Email validation on frontend
- Rate limiting (EmailJS built-in)
- No sensitive data exposure
- GDPR compliance ready
- Unsubscribe functionality

## Customization

You can customize:
- Email template design
- Subscription confirmation message
- Success/error messages
- Form styling
- Newsletter features list

## Support

For issues with EmailJS integration:
1. Check EmailJS dashboard for service status
2. Verify template ID and service ID
3. Check browser console for errors
4. Ensure domain is whitelisted
5. Verify email service configuration
