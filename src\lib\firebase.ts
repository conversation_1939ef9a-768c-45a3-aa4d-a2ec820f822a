// Firebase configuration for Certificate Management System
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';

// Firebase configuration for Certificate Generator Wolf
const firebaseConfig = {
  apiKey: "AIzaSyAIiu8HRCf1_gxIWW_ZjtuIlULEmHleXO8",
  authDomain: "cyber-wolf-certificate.firebaseapp.com",
  databaseURL: "https://cyber-wolf-certificate-default-rtdb.firebaseio.com",
  projectId: "cyber-wolf-certificate",
  storageBucket: "cyber-wolf-certificate.firebasestorage.app",
  messagingSenderId: "141757040542",
  appId: "1:141757040542:web:fdbf1e0d07470369d8788f",
  measurementId: "G-DH1PM08VJ0"
};

// Initialize Firebase
let app;
let db;
let auth;

try {
  app = initializeApp(firebaseConfig);
  db = getFirestore(app);
  auth = getAuth(app);
} catch (error) {
  console.error("Firebase initialization error:", error);
  console.log("Please configure Firebase properly. See FIREBASE_SETUP.md for instructions.");
}

export { db, auth };
export default app;
