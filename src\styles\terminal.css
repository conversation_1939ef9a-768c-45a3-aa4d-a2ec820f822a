/* Terminal Custom Scrollbar Styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-600 {
  scrollbar-color: #4b5563 transparent;
}

.scrollbar-track-gray-800 {
  scrollbar-color: #1f2937 transparent;
}

.scrollbar-track-gray-900 {
  scrollbar-color: #111827 transparent;
}

/* Webkit Scrollbar Styles for better browser support */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #111827;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Smooth transitions for better performance */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Optimized animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Terminal-specific optimizations */
.terminal-content {
  will-change: scroll-position;
  contain: layout style paint;
}

.terminal-line {
  contain: layout style;
}

/* Prevent text selection on UI elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Better focus styles */
.focus-enhanced:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

/* Mobile-optimized button sizes */
@media (max-width: 640px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .terminal-content {
    background: #000000;
    color: #ffffff;
  }
  
  .text-gray-300 {
    color: #e5e5e5;
  }
  
  .text-gray-400 {
    color: #d1d1d1;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-spin,
  .animate-pulse,
  .animate-fade-in {
    animation: none;
  }
  
  .transition-all {
    transition: none;
  }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
  .terminal-content {
    background: #000000;
  }
}
