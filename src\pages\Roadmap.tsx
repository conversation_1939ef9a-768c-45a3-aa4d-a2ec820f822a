import React, { useEffect, useState } from "react";
import { 
  Shield, 
  Code, 
  Database, 
  Globe, 
  Lock, 
  Terminal, 
  Bug, 
  Search, 
  Key, 
  FileText, 
  Zap, 
  Target, 
  Eye, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Award, 
  BookOpen, 
  Users,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Download,
  Play
} from "lucide-react";

const Roadmap = () => {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const [expandedModules, setExpandedModules] = useState<number[]>([]);

  const toggleModule = (moduleId: number) => {
    setExpandedModules(prev => 
      prev.includes(moduleId) 
        ? prev.filter(id => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  const courseModules = [
    { id: 1, title: "Brief Introduction to Web World and Web Technology", icon: <Globe className="h-5 w-5" />, category: "Foundation", tools: ["Browser DevTools", "HTML/CSS", "HTTP Protocol"], definition: "Understanding the fundamentals of web technology, internet architecture, and how web applications communicate over the internet." },
    { id: 2, title: "Introduction to the port 80 and port 443(SSL)", icon: <Lock className="h-5 w-5" />, category: "Network", tools: ["Nmap", "Netstat", "Wireshark"], definition: "Deep dive into HTTP (port 80) and HTTPS (port 443) protocols, SSL/TLS encryption, and secure communication channels." },
    { id: 3, title: "Introduction to Web languages", icon: <Code className="h-5 w-5" />, category: "Programming", tools: ["HTML", "CSS", "JavaScript", "PHP", "Python"], definition: "Essential web programming languages including client-side and server-side technologies for penetration testers." },
    { id: 4, title: "Examine Web Internal Architecture with web coding and Database", icon: <Database className="h-5 w-5" />, category: "Architecture", tools: ["MySQL", "PostgreSQL", "MongoDB", "SQLite"], definition: "Understanding web application architecture, MVC patterns, database integration, and backend-frontend communication." },
    { id: 5, title: "Examine Flag associated with Three-way Handshaking", icon: <Target className="h-5 w-5" />, category: "Network", tools: ["Wireshark", "TCPdump", "Nmap"], definition: "TCP handshake analysis, SYN/ACK/FIN flags, connection establishment, and network communication protocols." },
    { id: 6, title: "Configuring System for Web Hacking", icon: <Terminal className="h-5 w-5" />, category: "Setup", tools: ["Kali Linux", "VMware", "VirtualBox"], definition: "Setting up penetration testing environment, virtual machines, and essential tools for web application security testing." },
    { id: 7, title: "Metasploit and Metasploit Framework", icon: <Terminal className="h-5 w-5" />, category: "Exploitation", tools: ["Metasploit", "Msfconsole", "Meterpreter"], definition: "Comprehensive guide to Metasploit framework, exploit development, payload generation, and post-exploitation techniques." },
    { id: 8, title: "Examine Top Ten Vulnerability in Web Application (OWASP)", icon: <Shield className="h-5 w-5" />, category: "Vulnerabilities", tools: ["OWASP ZAP", "Burp Suite", "Nikto"], definition: "OWASP Top 10 vulnerabilities including injection, broken authentication, sensitive data exposure, and security misconfigurations." },
    { id: 9, title: "Web Proxies and How We Use Web Proxies for Penetration Testing", icon: <Search className="h-5 w-5" />, category: "Proxy", tools: ["Burp Suite", "OWASP ZAP", "Fiddler"], definition: "Web proxy configuration, traffic interception, request/response manipulation, and automated scanning techniques." },
    { id: 10, title: "SSL (Secure Socket Layer), SSL Exploitation and SSL Sniffing Technique", icon: <Lock className="h-5 w-5" />, category: "SSL/TLS", tools: ["SSLyze", "testssl.sh", "OpenSSL"], definition: "SSL/TLS protocol analysis, certificate validation, cipher suite testing, and SSL-based attack vectors." },
    { id: 11, title: "Different Web Framework for Web Penetration Testing", icon: <Code className="h-5 w-5" />, category: "Frameworks", tools: ["Django", "Flask", "Express.js", "Spring"], definition: "Framework-specific vulnerabilities, configuration issues, and testing methodologies for popular web frameworks." },
    { id: 12, title: "Netcat Lab for HTTP 1.0, 1.1 and 2.X etc", icon: <Terminal className="h-5 w-5" />, category: "Network", tools: ["Netcat", "Curl", "HTTPie"], definition: "HTTP protocol versions, manual request crafting, response analysis, and low-level network communication." },
    { id: 13, title: "HTTP Method Testing with Metasploit", icon: <Terminal className="h-5 w-5" />, category: "HTTP", tools: ["Metasploit", "Curl", "Burp Suite"], definition: "HTTP methods (GET, POST, PUT, DELETE, OPTIONS) testing, method tampering, and security implications." },
    { id: 14, title: "Attacking HTTP Authentication with Nmap and Metasploit", icon: <Key className="h-5 w-5" />, category: "Authentication", tools: ["Nmap", "Metasploit", "Hydra"], definition: "HTTP authentication mechanisms, brute force attacks, credential stuffing, and authentication bypass techniques." },
    { id: 15, title: "HTTP Digest Auth hashing RFC 2069", icon: <Key className="h-5 w-5" />, category: "Authentication", tools: ["Hashcat", "John the Ripper", "Burp Suite"], definition: "HTTP Digest authentication, MD5 hashing, nonce values, and cryptographic attack methods." },
    { id: 16, title: "HTTP-set Cookie with HTTP Cookie", icon: <FileText className="h-5 w-5" />, category: "Session", tools: ["Browser DevTools", "Burp Suite", "Cookie Editor"], definition: "Cookie mechanisms, session management, cookie attributes (HttpOnly, Secure, SameSite), and cookie-based attacks." },
    { id: 17, title: "SSL-TLS(Socket Oriented Protocol) Transport Layer Security", icon: <Lock className="h-5 w-5" />, category: "SSL/TLS", tools: ["OpenSSL", "SSLyze", "Nmap"], definition: "TLS handshake process, certificate chain validation, cipher negotiation, and transport layer security analysis." },
    { id: 18, title: "File Extraction from HTTP/HTTPS Traffic", icon: <FileText className="h-5 w-5" />, category: "Traffic Analysis", tools: ["Wireshark", "TCPdump", "NetworkMiner"], definition: "Network traffic analysis, file carving, protocol reconstruction, and data extraction from network captures." },
    { id: 19, title: "HTML Injection in Tag Parameter", icon: <Code className="h-5 w-5" />, category: "Injection", tools: ["Burp Suite", "OWASP ZAP", "Custom Scripts"], definition: "HTML injection techniques, tag parameter manipulation, DOM modification, and client-side code injection." },
    { id: 20, title: "HTML Injection- Bypass Filter CGI ESCAPE", icon: <Code className="h-5 w-5" />, category: "Injection", tools: ["Burp Suite", "Custom Payloads", "Encoding Tools"], definition: "Filter bypass techniques, encoding methods, CGI escape sequences, and advanced injection payloads." },
    { id: 21, title: "Web to Shell on the Server (Advanced Exploitation)", icon: <Terminal className="h-5 w-5" />, category: "Exploitation", tools: ["Metasploit", "Custom Shells", "Reverse Shells"], definition: "Web shell deployment, command execution, privilege escalation, and maintaining persistent access." },
    { id: 22, title: "Configure SSH SOCKS Proxy With Burp Suite To Tunnel All Testing Traffic Through A Cloud Server", icon: <Globe className="h-5 w-5" />, category: "Proxy", tools: ["SSH", "Burp Suite", "Cloud VPS"], definition: "SSH tunneling, SOCKS proxy configuration, traffic routing through cloud infrastructure, and anonymization techniques." },
    { id: 23, title: "Brief Introduction to XSS (Cross Site Scripting) & Tools Setup for Exploitation", icon: <Bug className="h-5 w-5" />, category: "XSS", tools: ["XSSHunter", "BeEF", "Burp Suite"], definition: "Cross-site scripting fundamentals, XSS types, payload crafting, and exploitation framework setup." },
    { id: 24, title: "XSS (Cross Site Scripting) Identification Process on Live Application", icon: <Bug className="h-5 w-5" />, category: "XSS", tools: ["Burp Suite", "OWASP ZAP", "XSStrike"], definition: "XSS vulnerability discovery, input validation testing, context analysis, and automated scanning techniques." },
    { id: 25, title: "XSS All Types (Persistent, Non-Persistent & DOM Based) Exploitation on Live Application", icon: <Bug className="h-5 w-5" />, category: "XSS", tools: ["Custom Payloads", "BeEF", "XSSHunter"], definition: "Stored XSS, reflected XSS, DOM-based XSS exploitation, payload delivery, and impact demonstration." },
    { id: 26, title: "XSS via Event Handler Attributes", icon: <Bug className="h-5 w-5" />, category: "XSS", tools: ["JavaScript", "Browser DevTools", "Custom Payloads"], definition: "Event-driven XSS attacks using onclick, onload, onmouseover handlers and DOM event manipulation." },
    { id: 27, title: "JavaScript for Penetration Tester- Loop, function and Data Types", icon: <Code className="h-5 w-5" />, category: "Programming", tools: ["JavaScript", "Node.js", "Browser Console"], definition: "JavaScript fundamentals for security testing: loops, functions, data types, and DOM manipulation." },
    { id: 28, title: "JavaScript For Penetration: Stealing Cookie and Advanced Form Manipulations", icon: <Code className="h-5 w-5" />, category: "Programming", tools: ["JavaScript", "Browser DevTools", "Custom Scripts"], definition: "Cookie theft techniques, form manipulation, session hijacking, and client-side exploitation methods." },
    { id: 29, title: "Null file Injection Technique", icon: <FileText className="h-5 w-5" />, category: "Injection", tools: ["Burp Suite", "Custom Scripts", "File Upload Tools"], definition: "Null byte injection, file extension bypass, upload filter evasion, and path traversal techniques." },
    { id: 30, title: "LFI (Local File Inclusion) & RFI (Remote File Inclusion) Exploitation on Live Application", icon: <FileText className="h-5 w-5" />, category: "Injection", tools: ["Burp Suite", "Custom Scripts", "File Inclusion Tools"], definition: "File inclusion vulnerabilities, directory traversal, remote code execution, and log poisoning attacks." },
    { id: 31, title: "Session Management in Depth, Session Hijacking , Cookie and Token Based Attack on Live Application", icon: <Key className="h-5 w-5" />, category: "Session", tools: ["Burp Suite", "Session Tools", "Token Analyzers"], definition: "Session lifecycle, token generation, session fixation, hijacking techniques, and token-based attacks." },
    { id: 32, title: "Use Burp Suite To Identify HTTP Request That Are Vulnerable To Open Redirect Attacks (Live Application)", icon: <Search className="h-5 w-5" />, category: "Redirect", tools: ["Burp Suite", "OWASP ZAP", "Custom Scripts"], definition: "Open redirect vulnerability identification, URL manipulation, phishing attack vectors, and automated detection." },
    { id: 33, title: "MIME Sniffing Vulnerabilities & Exploitation", icon: <FileText className="h-5 w-5" />, category: "MIME", tools: ["Burp Suite", "File Analysis Tools", "MIME Sniffers"], definition: "MIME type confusion, content sniffing attacks, file upload bypass, and content-type manipulation." },
    { id: 34, title: "Same-Origin-Policy, CORS (Cross Origin Resource Sharing) Identification & Exploitation on Live Application", icon: <Globe className="h-5 w-5" />, category: "CORS", tools: ["Browser DevTools", "Burp Suite", "CORS Tools"], definition: "Same-origin policy bypass, CORS misconfigurations, cross-domain attacks, and origin validation flaws." },
    { id: 35, title: "Authentication and Authorization Bypass (Live Application Assessment, Cloud Azure AD Assessment , Vertical PE , Horizontal PE)", icon: <Key className="h-5 w-5" />, category: "Authentication", tools: ["Burp Suite", "Azure Tools", "Auth Bypass Tools"], definition: "Authentication bypass techniques, privilege escalation (vertical/horizontal), cloud identity attacks, and access control flaws." },
    { id: 36, title: "Crypto Attack (Advanced Technique): Algo, PKI, Hash Cracking (All Types) Attacks etc.", icon: <Lock className="h-5 w-5" />, category: "Cryptography", tools: ["Hashcat", "John the Ripper", "OpenSSL"], definition: "Cryptographic attacks, algorithm weaknesses, PKI vulnerabilities, hash collision, and rainbow table attacks." },
    { id: 37, title: "Advanced Burp Hacks for Bug Bounty Hunters", icon: <Search className="h-5 w-5" />, category: "Tools", tools: ["Burp Suite", "Extensions", "Custom Scripts"], definition: "Advanced Burp Suite techniques, extension development, automation scripts, and bug bounty methodologies." },
    { id: 38, title: "Browser Fuzzing: Fuzz Popular Browsers Such As Google Chrome, Firefox and Safari", icon: <Bug className="h-5 w-5" />, category: "Fuzzing", tools: ["AFL", "Browser Fuzzers", "Custom Tools"], definition: "Browser vulnerability research, fuzzing techniques, crash analysis, and exploit development for browser bugs." },
    { id: 39, title: "Automate HTML Fuzzing : Live Exploitation", icon: <Bug className="h-5 w-5" />, category: "Fuzzing", tools: ["Custom Fuzzers", "Automation Scripts", "HTML Parsers"], definition: "Automated HTML fuzzing, DOM manipulation testing, parser confusion attacks, and vulnerability discovery." },
    { id: 40, title: "Brief Introduction to Database: MYSQL, NOSQL, MONGO DB, POSTGRES, AURORA, SQLITE3 etc", icon: <Database className="h-5 w-5" />, category: "Database", tools: ["MySQL", "MongoDB", "PostgreSQL", "SQLite"], definition: "Database fundamentals, SQL/NoSQL differences, database architectures, and security considerations." },
    { id: 41, title: "Fuzzing the JavaScript Engine", icon: <Bug className="h-5 w-5" />, category: "Fuzzing", tools: ["JavaScript Fuzzers", "V8 Tools", "SpiderMonkey"], definition: "JavaScript engine vulnerability research, JIT compiler bugs, memory corruption, and exploit development." },
    { id: 42, title: "Official Hacker Associate Web & Cloud API Assessment Mind Map", icon: <Target className="h-5 w-5" />, category: "Methodology", tools: ["Mind Mapping Tools", "Assessment Frameworks", "API Tools"], definition: "Comprehensive assessment methodology, cloud API security testing, and structured penetration testing approach." },
    { id: 43, title: "Examine Web Coding & Learn How to Write Code for Web", icon: <Code className="h-5 w-5" />, category: "Programming", tools: ["HTML", "CSS", "JavaScript", "PHP"], definition: "Web development fundamentals, secure coding practices, and understanding developer perspectives for better testing." },
    { id: 44, title: "Cloud Web & API Exploitation & Security: OWASP TOP 10 for Cloud", icon: <Globe className="h-5 w-5" />, category: "Cloud", tools: ["AWS Tools", "Azure CLI", "Cloud Security Tools"], definition: "Cloud-specific vulnerabilities, API security testing, serverless attacks, and cloud OWASP Top 10." },
    { id: 45, title: "Examine Iframe Vulnerability & Exploitation on a Live Web Application", icon: <Globe className="h-5 w-5" />, category: "Web", tools: ["Browser DevTools", "Iframe Tools", "Custom Scripts"], definition: "Iframe security issues, clickjacking attacks, frame busting bypass, and cross-frame scripting." },
    { id: 46, title: "Stress Penetration Testing on Live Web Application : DOS and DDOS Attacks", icon: <Zap className="h-5 w-5" />, category: "DoS", tools: ["LOIC", "HOIC", "Stress Testing Tools"], definition: "Denial of service attacks, distributed attacks, application layer DoS, and stress testing methodologies." },
    { id: 47, title: "Offensive Penetration Testing for Web (Black hat Approach)", icon: <AlertTriangle className="h-5 w-5" />, category: "Offensive", tools: ["Advanced Tools", "Custom Exploits", "Stealth Techniques"], definition: "Advanced offensive techniques, stealth methods, anti-forensics, and sophisticated attack vectors." },
    { id: 48, title: "JavaScript Based Attack & Exploitation on Live Web Application", icon: <Code className="h-5 w-5" />, category: "JavaScript", tools: ["JavaScript", "Browser Exploits", "Client-side Tools"], definition: "Client-side attacks, JavaScript exploitation, browser-based attacks, and DOM manipulation techniques." },
    { id: 49, title: "HTTP Methods and Verb Tempering Attack & Analysis", icon: <Terminal className="h-5 w-5" />, category: "HTTP", tools: ["Burp Suite", "HTTP Tools", "Method Override"], definition: "HTTP method manipulation, verb tampering, method override attacks, and REST API security." },
    { id: 50, title: "HTTP Basic Authentication", icon: <Key className="h-5 w-5" />, category: "Authentication", tools: ["Base64 Tools", "Brute Force Tools", "Credential Tools"], definition: "Basic authentication mechanism, base64 encoding, credential transmission, and authentication bypass." },
    { id: 51, title: "HTTP Digest Authentication RFC 2069", icon: <Key className="h-5 w-5" />, category: "Authentication", tools: ["Digest Tools", "Hash Crackers", "Auth Analyzers"], definition: "Digest authentication protocol, MD5 hashing, nonce handling, and cryptographic weaknesses." },
    { id: 52, title: "HTTP Statelessness and Cookie", icon: <FileText className="h-5 w-5" />, category: "Session", tools: ["Cookie Tools", "Session Analyzers", "State Management"], definition: "HTTP stateless nature, cookie-based state management, session tracking, and state manipulation attacks." },
    { id: 53, title: "Session ID and Cookie Stealing (Cookie/Token Attack) on Live Application", icon: <Key className="h-5 w-5" />, category: "Session", tools: ["Session Tools", "Cookie Stealers", "XSS Payloads"], definition: "Session hijacking techniques, cookie theft, token stealing, and session-based attack vectors." },
    { id: 54, title: "SSL MITM using Proxies", icon: <Lock className="h-5 w-5" />, category: "SSL/TLS", tools: ["SSL Proxies", "Certificate Tools", "MITM Frameworks"], definition: "SSL man-in-the-middle attacks, certificate spoofing, proxy-based interception, and TLS downgrade attacks." },
    { id: 55, title: "HTML Injection All Types", icon: <Code className="h-5 w-5" />, category: "Injection", tools: ["HTML Injection Tools", "Payload Generators", "Encoding Tools"], definition: "Comprehensive HTML injection techniques, context-based attacks, filter bypass, and payload optimization." },
    { id: 56, title: "HTML Injection Using 3rd Party Data Sources", icon: <Code className="h-5 w-5" />, category: "Injection", tools: ["API Tools", "Data Sources", "Integration Tools"], definition: "Third-party data injection, API-based attacks, external data manipulation, and supply chain attacks." },
    { id: 57, title: "Command Injection Technique", icon: <Terminal className="h-5 w-5" />, category: "Injection", tools: ["Command Injection Tools", "Shell Tools", "Payload Crafters"], definition: "OS command injection, shell command execution, blind command injection, and system-level exploitation." },
    { id: 58, title: "Web Shell PHP Meterpreter", icon: <Terminal className="h-5 w-5" />, category: "Web Shell", tools: ["PHP Shells", "Meterpreter", "Web Shell Tools"], definition: "PHP-based web shells, Meterpreter integration, persistent access, and post-exploitation techniques." },
    { id: 59, title: "Web Shell Using Python,PHP etc", icon: <Terminal className="h-5 w-5" />, category: "Web Shell", tools: ["Python Shells", "PHP Shells", "Custom Shells"], definition: "Multi-language web shells, shell development, obfuscation techniques, and detection evasion." },
    { id: 60, title: "JavaScript for Penetration Tester-Introduction to Hello World", icon: <Code className="h-5 w-5" />, category: "Programming", tools: ["JavaScript", "Browser Console", "Development Tools"], definition: "JavaScript basics for security testing, syntax fundamentals, and browser-based scripting." },
    { id: 61, title: "JavaScript for Penetration Tester: Variable", icon: <Code className="h-5 w-5" />, category: "Programming", tools: ["JavaScript", "Variable Tools", "Debugging Tools"], definition: "JavaScript variables, scope, data types, and variable manipulation for security testing." },
    { id: 62, title: "JavaScript for Penetration Tester: Operator", icon: <Code className="h-5 w-5" />, category: "Programming", tools: ["JavaScript", "Operator Tools", "Logic Analyzers"], definition: "JavaScript operators, logical operations, comparison operators, and operator precedence in security contexts." },
    { id: 63, title: "JavaScript for Penetration Tester-Conditionals", icon: <Code className="h-5 w-5" />, category: "Programming", tools: ["JavaScript", "Conditional Tools", "Flow Control"], definition: "Conditional statements, if-else logic, switch statements, and control flow manipulation." },
    { id: 64, title: "JavaScript for Penetration: Enumerating Data Properties", icon: <Code className="h-5 w-5" />, category: "Programming", tools: ["JavaScript", "Object Tools", "Property Analyzers"], definition: "Object property enumeration, prototype chain analysis, and data structure manipulation." },
    { id: 65, title: "File Upload Vulnerabilities & Exploitation (Advanced Methods)", icon: <FileText className="h-5 w-5" />, category: "File Upload", tools: ["Upload Tools", "File Analyzers", "Bypass Tools"], definition: "Advanced file upload attacks, MIME type bypass, double extension attacks, and malicious file execution." },
    { id: 66, title: "Invalidated Redirect", icon: <Globe className="h-5 w-5" />, category: "Redirect", tools: ["Redirect Tools", "URL Analyzers", "Validation Bypass"], definition: "Unvalidated redirect vulnerabilities, URL manipulation, phishing attacks, and redirect chain exploitation." },
    { id: 67, title: "CSRF (Client Side Request Forgery) Identification & Exploitation All Types: Live Application", icon: <Bug className="h-5 w-5" />, category: "CSRF", tools: ["CSRF Tools", "Token Analyzers", "Request Forgers"], definition: "Cross-site request forgery attacks, token bypass, SameSite cookie bypass, and CSRF exploitation techniques." },
    { id: 68, title: "Encoding Sniffing", icon: <Search className="h-5 w-5" />, category: "Encoding", tools: ["Encoding Tools", "Character Analyzers", "Sniffing Tools"], definition: "Character encoding detection, encoding-based attacks, charset confusion, and encoding bypass techniques." },
    { id: 69, title: "Null Termination Vulnerability", icon: <Bug className="h-5 w-5" />, category: "Injection", tools: ["Null Byte Tools", "String Analyzers", "Termination Tools"], definition: "Null byte injection, string termination attacks, path truncation, and null character exploitation." },
    { id: 70, title: "SSRF (Server Side Request Forgery) Identification & Exploitation All Types: Live Application", icon: <Globe className="h-5 w-5" />, category: "SSRF", tools: ["SSRF Tools", "Request Forgers", "Internal Scanners"], definition: "Server-side request forgery, internal network access, cloud metadata attacks, and SSRF exploitation chains." },
    { id: 71, title: "Threat Modeling", icon: <Target className="h-5 w-5" />, category: "Methodology", tools: ["Threat Modeling Tools", "Risk Assessment", "Attack Trees"], definition: "Systematic threat identification, risk assessment, attack surface analysis, and security architecture review." },
    { id: 72, title: "Generation of POC (Proof of Concept) on a Live Application", icon: <FileText className="h-5 w-5" />, category: "Documentation", tools: ["POC Tools", "Documentation Tools", "Exploit Frameworks"], definition: "Proof of concept development, exploit demonstration, impact assessment, and vulnerability documentation." },
    { id: 73, title: "Cloud API Security Assessment", icon: <Globe className="h-5 w-5" />, category: "Cloud", tools: ["API Tools", "Cloud Scanners", "Authentication Tools"], definition: "Cloud API security testing, authentication flaws, rate limiting bypass, and cloud-specific attack vectors." },
    { id: 74, title: "Red Teaming Tools for Web Application Penetration Testing", icon: <AlertTriangle className="h-5 w-5" />, category: "Red Team", tools: ["Red Team Tools", "C2 Frameworks", "Persistence Tools"], definition: "Advanced red team techniques, command and control, persistence mechanisms, and stealth operations." },
    { id: 75, title: "WAF Bypassing", icon: <Shield className="h-5 w-5" />, category: "WAF", tools: ["WAF Bypass Tools", "Payload Encoders", "Evasion Tools"], definition: "Web Application Firewall bypass techniques, payload obfuscation, rule evasion, and filter circumvention." },
    { id: 76, title: "Source Code Analysis", icon: <Code className="h-5 w-5" />, category: "Code Review", tools: ["SAST Tools", "Code Analyzers", "Static Analysis"], definition: "Static code analysis, vulnerability identification in source code, secure code review, and automated scanning." },
    { id: 77, title: "Live Subdomain Takeover", icon: <Globe className="h-5 w-5" />, category: "Subdomain", tools: ["Subdomain Tools", "DNS Tools", "Takeover Tools"], definition: "Subdomain takeover attacks, DNS hijacking, service misconfiguration exploitation, and domain security." },
    { id: 78, title: "Broken Link Hijacking", icon: <Globe className="h-5 w-5" />, category: "Link", tools: ["Link Analyzers", "Hijacking Tools", "Domain Tools"], definition: "Broken link exploitation, domain hijacking, expired domain attacks, and link-based vulnerabilities." },
    { id: 79, title: "RECOX-Engine", icon: <Search className="h-5 w-5" />, category: "Tools", tools: ["RECOX", "Reconnaissance Tools", "OSINT Tools"], definition: "Advanced reconnaissance engine, information gathering, OSINT techniques, and target profiling." },
    { id: 80, title: "Injection : All Types", icon: <Bug className="h-5 w-5" />, category: "Injection", tools: ["Injection Tools", "Payload Libraries", "Automated Scanners"], definition: "Comprehensive injection attack types: SQL, NoSQL, LDAP, XPath, OS command, and template injection." },
    { id: 81, title: "HTTP Request Smuggling", icon: <Globe className="h-5 w-5" />, category: "HTTP", tools: ["Smuggling Tools", "HTTP Analyzers", "Request Crafters"], definition: "HTTP request smuggling attacks, desynchronization techniques, cache poisoning, and protocol-level exploitation." },
    { id: 82, title: "Live Web Application Audit using Professional Web App Exploitation Framework", icon: <Shield className="h-5 w-5" />, category: "Audit", tools: ["Professional Frameworks", "Audit Tools", "Reporting Tools"], definition: "Comprehensive web application security audit, professional testing methodologies, and enterprise-grade assessment." },
    { id: 83, title: "Open Redirect Vulnerabilities Identification & Exploitation All Types: Live Application", icon: <Globe className="h-5 w-5" />, category: "Redirect", tools: ["Redirect Tools", "URL Validators", "Phishing Tools"], definition: "Open redirect vulnerability types, exploitation techniques, phishing attack vectors, and redirect chain analysis." },
    { id: 84, title: "OAuth2.0 Attacks & Security", icon: <Key className="h-5 w-5" />, category: "OAuth", tools: ["OAuth Tools", "Token Analyzers", "Flow Testers"], definition: "OAuth 2.0 security flaws, authorization code attacks, token hijacking, and identity provider exploitation." },
    { id: 85, title: "JSON Web Token Attacks & Security", icon: <Key className="h-5 w-5" />, category: "JWT", tools: ["JWT Tools", "Token Crackers", "Signature Validators"], definition: "JWT vulnerabilities, signature bypass, algorithm confusion, token manipulation, and cryptographic attacks." },
    { id: 86, title: "Hacker Associate Custom Payloads for All Types of Attacks Captcha Attack", icon: <Bug className="h-5 w-5" />, category: "Payloads", tools: ["Payload Generators", "Custom Scripts", "Captcha Tools"], definition: "Custom payload development, attack automation, CAPTCHA bypass techniques, and specialized exploit creation." },
    { id: 87, title: "Black Hat Tools & Technique", icon: <AlertTriangle className="h-5 w-5" />, category: "Black Hat", tools: ["Advanced Tools", "Stealth Techniques", "Underground Tools"], definition: "Advanced black hat methodologies, stealth techniques, anti-detection methods, and sophisticated attack tools." },
    { id: 88, title: "IP Rotating", icon: <Globe className="h-5 w-5" />, category: "Anonymity", tools: ["Proxy Tools", "VPN Services", "IP Rotation Tools"], definition: "IP address rotation techniques, proxy chaining, anonymization methods, and detection evasion." },
    { id: 89, title: "Insufficient Anti Automation Attack & Security", icon: <Bug className="h-5 w-5" />, category: "Automation", tools: ["Automation Tools", "Rate Limiting Bypass", "Bot Tools"], definition: "Anti-automation bypass, rate limiting evasion, bot detection circumvention, and automated attack techniques." },
    { id: 90, title: "MFA Bypassing Technique", icon: <Key className="h-5 w-5" />, category: "MFA", tools: ["MFA Bypass Tools", "2FA Tools", "Authentication Bypass"], definition: "Multi-factor authentication bypass, 2FA circumvention, social engineering attacks, and authentication weaknesses." },
    { id: 91, title: "Burp Extension : Writing Your Own Burp Extension", icon: <Code className="h-5 w-5" />, category: "Tools", tools: ["Burp Suite", "Java", "Python", "Extension APIs"], definition: "Burp Suite extension development, API usage, custom tool creation, and automation framework development." },
    { id: 92, title: "Advanced Burp Hacks for Bounty Hunters", icon: <Search className="h-5 w-5" />, category: "Bug Bounty", tools: ["Burp Suite", "Advanced Extensions", "Automation Scripts"], definition: "Advanced Burp techniques for bug bounty hunting, automation strategies, and professional testing methodologies." },
    { id: 93, title: "Blind XSS", icon: <Bug className="h-5 w-5" />, category: "XSS", tools: ["Blind XSS Tools", "XSS Hunters", "Callback Services"], definition: "Blind cross-site scripting attacks, out-of-band exploitation, callback mechanisms, and delayed payload execution." },
    { id: 94, title: "Captcha Attack", icon: <Eye className="h-5 w-5" />, category: "Captcha", tools: ["OCR Tools", "Captcha Solvers", "ML Tools"], definition: "CAPTCHA bypass techniques, optical character recognition, machine learning approaches, and automated solving." },
    { id: 95, title: "Clickjacking Attack", icon: <Eye className="h-5 w-5" />, category: "Clickjacking", tools: ["Iframe Tools", "UI Redressing", "Frame Busters"], definition: "Clickjacking attacks, UI redressing, iframe manipulation, frame busting bypass, and user interface deception." },
    { id: 96, title: "CAT Framework for Web Application Penetration Tester", icon: <Target className="h-5 w-5" />, category: "Framework", tools: ["CAT Framework", "Testing Tools", "Methodology Tools"], definition: "Comprehensive Assessment Toolkit framework, structured testing methodology, and professional assessment approach." },
    { id: 97, title: "XXE (External Entity Injection) Identification & Exploitation All Types: Live Application", icon: <Bug className="h-5 w-5" />, category: "XXE", tools: ["XXE Tools", "XML Parsers", "Entity Injectors"], definition: "XML External Entity attacks, file disclosure, SSRF via XXE, blind XXE, and XML parser exploitation." },
    { id: 98, title: "Adversary Simulation of a Live Attack using Cloud VPS", icon: <Globe className="h-5 w-5" />, category: "Red Team", tools: ["Cloud VPS", "Attack Simulation", "C2 Frameworks"], definition: "Real-world attack simulation, cloud-based operations, adversary emulation, and advanced persistent threat simulation." },
    { id: 99, title: "Obscure Email Vulnerability", icon: <FileText className="h-5 w-5" />, category: "Email", tools: ["Email Tools", "SMTP Analyzers", "Mail Security"], definition: "Email-based vulnerabilities, SMTP injection, email spoofing, and mail server exploitation techniques." },
    { id: 100, title: "Email Attacking Vector", icon: <FileText className="h-5 w-5" />, category: "Email", tools: ["Email Attack Tools", "Phishing Frameworks", "Social Engineering"], definition: "Email attack vectors, phishing campaigns, social engineering, and email-based exploitation methods." },
    { id: 101, title: "Server Side Template Injection", icon: <Code className="h-5 w-5" />, category: "Injection", tools: ["Template Tools", "SSTI Scanners", "Payload Generators"], definition: "Server-side template injection attacks, template engine exploitation, code execution, and template security." },
    { id: 102, title: "Web Sockets Exploitation", icon: <Globe className="h-5 w-5" />, category: "WebSocket", tools: ["WebSocket Tools", "Real-time Analyzers", "Socket Testers"], definition: "WebSocket security testing, real-time communication attacks, message manipulation, and protocol-level exploitation." },
    { id: 103, title: "Complete Web Application Audit : Report Writing. Basic To Advance", icon: <FileText className="h-5 w-5" />, category: "Reporting", tools: ["Report Tools", "Documentation", "Audit Frameworks"], definition: "Professional report writing, vulnerability documentation, executive summaries, and comprehensive audit reporting." }
  ];

  const tools = [
    { name: "Burp Suite", icon: <Shield className="h-6 w-6" />, category: "Web Proxy" },
    { name: "Metasploit", icon: <Terminal className="h-6 w-6" />, category: "Exploitation" },
    { name: "Nmap", icon: <Search className="h-6 w-6" />, category: "Network Scanner" },
    { name: "OWASP ZAP", icon: <Bug className="h-6 w-6" />, category: "Security Scanner" },
    { name: "Wireshark", icon: <Eye className="h-6 w-6" />, category: "Network Analysis" },
    { name: "SQLMap", icon: <Database className="h-6 w-6" />, category: "SQL Injection" },
    { name: "Netcat", icon: <Terminal className="h-6 w-6" />, category: "Network Utility" },
    { name: "Nikto", icon: <Search className="h-6 w-6" />, category: "Web Scanner" }
  ];

  return (
    <div className="pt-20 min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT">
      {/* Hero Section */}
      <section className="py-20 px-6 md:px-12 relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-12">
            <div className="inline-block bg-psyco-green-DEFAULT text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
              🎯 Complete Course Roadmap
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in">
              Web Penetration Testing Course Syllabus
            </h1>
            <p className="text-xl text-gray-300 mb-8 animate-fade-in animation-delay-100 max-w-3xl mx-auto">
              Master web application security with our comprehensive 103-module course covering everything from basics to advanced exploitation techniques.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in animation-delay-200">
              <a
                href="https://cyberwolf-career-guidance.web.app/registration.html"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-3 px-8 rounded-lg transition-all duration-300 btn-glow"
              >
                <Play className="mr-2 h-5 w-5" />
                Start Learning Now
              </a>
              <a
                href="https://cyberwolf-career-guidance.web.app/details.html"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center bg-transparent border border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10 font-medium py-3 px-8 rounded-lg transition-all duration-300"
              >
                <Download className="mr-2 h-5 w-5" />
                Download Syllabus
              </a>
            </div>
          </div>

          {/* Course Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            <div className="glassmorphism p-6 text-center">
              <BookOpen className="h-8 w-8 text-psyco-green-DEFAULT mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">103</div>
              <div className="text-gray-400 text-sm">Modules</div>
            </div>
            <div className="glassmorphism p-6 text-center">
              <Clock className="h-8 w-8 text-psyco-green-DEFAULT mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">200+</div>
              <div className="text-gray-400 text-sm">Hours</div>
            </div>
            <div className="glassmorphism p-6 text-center">
              <Target className="h-8 w-8 text-psyco-green-DEFAULT mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">50+</div>
              <div className="text-gray-400 text-sm">Tools</div>
            </div>
            <div className="glassmorphism p-6 text-center">
              <Award className="h-8 w-8 text-psyco-green-DEFAULT mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">Live</div>
              <div className="text-gray-400 text-sm">Projects</div>
            </div>
          </div>
        </div>
      </section>

      {/* Tools Section */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Professional Tools You'll Master</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Industry-standard tools and frameworks used by professional penetration testers
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6">
            {tools.map((tool, index) => (
              <div 
                key={index}
                className="glassmorphism p-4 text-center hover:transform hover:scale-105 transition-all duration-300"
              >
                <div className="text-psyco-green-DEFAULT mb-2 flex justify-center">
                  {tool.icon}
                </div>
                <h3 className="text-white font-medium text-sm mb-1">{tool.name}</h3>
                <p className="text-gray-400 text-xs">{tool.category}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Course Modules */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Complete Course Modules</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Comprehensive 103-module curriculum covering all aspects of web penetration testing
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {courseModules.map((module, index) => (
              <div
                key={module.id}
                className="glassmorphism overflow-hidden hover:transform hover:scale-105 transition-all duration-300"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className="text-psyco-green-DEFAULT mr-3">
                        {module.icon}
                      </div>
                      <div className="text-psyco-green-DEFAULT text-sm font-medium">
                        Module {module.id}
                      </div>
                    </div>
                    <span className="bg-psyco-green-DEFAULT/20 text-psyco-green-DEFAULT px-2 py-1 rounded-full text-xs">
                      {module.category}
                    </span>
                  </div>

                  <h3 className="text-white font-semibold mb-3 text-sm leading-tight">
                    {module.title}
                  </h3>

                  <p className="text-gray-300 text-xs mb-3 leading-relaxed">
                    {module.definition}
                  </p>

                  <div className="mb-4">
                    <p className="text-gray-400 text-xs mb-2">Tools & Technologies:</p>
                    <div className="flex flex-wrap gap-1">
                      {module.tools.map((tool, toolIndex) => (
                        <span
                          key={toolIndex}
                          className="bg-psyco-black-DEFAULT text-gray-300 px-2 py-1 rounded text-xs"
                        >
                          {tool}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <CheckCircle className="h-4 w-4 text-psyco-green-DEFAULT" />
                    <span className="text-gray-400 text-xs">Hands-on Lab Included</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <div className="glassmorphism p-6 inline-block">
              <p className="text-gray-300 mb-4">
                <strong className="text-white">All 103 modules displayed</strong> - Complete comprehensive web penetration testing syllabus
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="https://cyberwolf-career-guidance.web.app/registration.html"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-2 px-6 rounded-lg transition-all duration-300"
                >
                  <Play className="mr-2 h-4 w-4" />
                  Enroll Now
                </a>
                <a
                  href="https://cyberwolf-career-guidance.web.app/details.html"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center bg-transparent border border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10 font-medium py-2 px-6 rounded-lg transition-all duration-300"
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Course Details
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Scripts Section */}
      <section className="py-16 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Professional Penetration Testing Scripts</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Advanced automation scripts and tools developed for professional web application security testing
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Script 1: Automated Vulnerability Scanner */}
            <div className="glassmorphism p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-lg flex items-center justify-center mr-4">
                  <Search className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">Automated Vulnerability Scanner</h3>
                  <p className="text-gray-400 text-sm">Python-based comprehensive scanner</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4 text-sm">
                Advanced Python script that automates the detection of OWASP Top 10 vulnerabilities including SQL injection, XSS, CSRF, and authentication bypass. Features multi-threading, custom payload generation, and detailed reporting.
              </p>
              <div className="mb-4">
                <p className="text-gray-400 text-xs mb-2">Key Features:</p>
                <ul className="text-gray-300 text-xs space-y-1">
                  <li>• Multi-threaded scanning for faster results</li>
                  <li>• Custom payload generation and encoding</li>
                  <li>• WAF detection and bypass techniques</li>
                  <li>• Comprehensive HTML/PDF reporting</li>
                  <li>• Integration with Burp Suite and OWASP ZAP</li>
                </ul>
              </div>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Python</span>
                <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Requests</span>
                <span className="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">BeautifulSoup</span>
                <span className="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">Threading</span>
              </div>
            </div>

            {/* Script 2: Advanced XSS Payload Generator */}
            <div className="glassmorphism p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-lg flex items-center justify-center mr-4">
                  <Bug className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">Advanced XSS Payload Generator</h3>
                  <p className="text-gray-400 text-sm">Context-aware XSS exploitation tool</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4 text-sm">
                Intelligent XSS payload generator that analyzes HTML context, DOM structure, and input validation to create highly effective, context-specific XSS payloads. Includes filter bypass techniques and obfuscation methods.
              </p>
              <div className="mb-4">
                <p className="text-gray-400 text-xs mb-2">Key Features:</p>
                <ul className="text-gray-300 text-xs space-y-1">
                  <li>• Context-aware payload generation</li>
                  <li>• DOM-based XSS detection and exploitation</li>
                  <li>• Advanced filter bypass techniques</li>
                  <li>• JavaScript obfuscation and encoding</li>
                  <li>• Blind XSS callback integration</li>
                </ul>
              </div>
              <div className="flex flex-wrap gap-2">
                <span className="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">JavaScript</span>
                <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Python</span>
                <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">DOM Parser</span>
                <span className="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">Selenium</span>
              </div>
            </div>

            {/* Script 3: SQL Injection Automation Framework */}
            <div className="glassmorphism p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-lg flex items-center justify-center mr-4">
                  <Database className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">SQL Injection Automation Framework</h3>
                  <p className="text-gray-400 text-sm">Advanced SQLi detection and exploitation</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4 text-sm">
                Comprehensive SQL injection framework supporting multiple database types (MySQL, PostgreSQL, MSSQL, Oracle). Features blind SQLi detection, time-based attacks, union-based exploitation, and automated data extraction.
              </p>
              <div className="mb-4">
                <p className="text-gray-400 text-xs mb-2">Key Features:</p>
                <ul className="text-gray-300 text-xs space-y-1">
                  <li>• Multi-database support and fingerprinting</li>
                  <li>• Blind and time-based SQLi detection</li>
                  <li>• Automated data extraction and dumping</li>
                  <li>• WAF bypass and evasion techniques</li>
                  <li>• Custom payload generation and testing</li>
                </ul>
              </div>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Python</span>
                <span className="bg-orange-500/20 text-orange-400 px-2 py-1 rounded text-xs">SQLAlchemy</span>
                <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Regex</span>
                <span className="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">Threading</span>
              </div>
            </div>

            {/* Script 4: Web Application Reconnaissance Suite */}
            <div className="glassmorphism p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-psyco-green-DEFAULT rounded-lg flex items-center justify-center mr-4">
                  <Eye className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">Web Application Reconnaissance Suite</h3>
                  <p className="text-gray-400 text-sm">Comprehensive OSINT and enumeration tool</p>
                </div>
              </div>
              <p className="text-gray-300 mb-4 text-sm">
                Advanced reconnaissance framework for web applications featuring subdomain enumeration, technology fingerprinting, directory brute-forcing, and OSINT gathering. Integrates multiple data sources for comprehensive target profiling.
              </p>
              <div className="mb-4">
                <p className="text-gray-400 text-xs mb-2">Key Features:</p>
                <ul className="text-gray-300 text-xs space-y-1">
                  <li>• Subdomain enumeration and takeover detection</li>
                  <li>• Technology stack fingerprinting</li>
                  <li>• Directory and file brute-forcing</li>
                  <li>• Social media and OSINT integration</li>
                  <li>• Automated report generation</li>
                </ul>
              </div>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Python</span>
                <span className="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">APIs</span>
                <span className="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">DNS</span>
                <span className="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">OSINT</span>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <div className="glassmorphism p-6 inline-block">
              <p className="text-gray-300 mb-4">
                <strong className="text-white">Professional Scripts Included</strong> - All scripts provided with source code and documentation
              </p>
              <a
                href="https://cyberwolf-career-guidance.web.app/registration.html"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-2 px-6 rounded-lg transition-all duration-300"
              >
                <Code className="mr-2 h-4 w-4" />
                Access Scripts & Tools
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Course Provider */}
      <section className="py-16 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="glassmorphism p-8 text-center">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mr-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Course Provided by Cyber Wolf</h2>
                <p className="text-gray-400">Leading Cybersecurity Training Institute</p>
              </div>
            </div>
            <p className="text-gray-300 max-w-3xl mx-auto mb-6">
              Cyber Wolf is a premier cybersecurity training institute dedicated to providing world-class education in ethical hacking, penetration testing, and cybersecurity. Our comprehensive curriculum is designed by industry experts and updated regularly to reflect the latest threats and techniques.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <span className="bg-psyco-green-DEFAULT/20 text-psyco-green-DEFAULT px-3 py-1 rounded-full text-sm">Expert Instructors</span>
              <span className="bg-psyco-green-DEFAULT/20 text-psyco-green-DEFAULT px-3 py-1 rounded-full text-sm">Hands-on Labs</span>
              <span className="bg-psyco-green-DEFAULT/20 text-psyco-green-DEFAULT px-3 py-1 rounded-full text-sm">Industry Certification</span>
              <span className="bg-psyco-green-DEFAULT/20 text-psyco-green-DEFAULT px-3 py-1 rounded-full text-sm">Career Support</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Roadmap;
