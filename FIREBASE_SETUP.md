# Firebase Setup Instructions for Certificate Management System

## ✅ Configuration Status
**Firebase configuration is already set up!**
- Project: `certificate-generator-wolf`
- Configuration: Updated in `src/lib/firebase.ts`

## Prerequisites
1. Install Firebase dependency:
```bash
npm install firebase
```

## Firebase Project Setup (Already Configured)

### ✅ Firebase Project
- **Project Name**: certificate-generator-wolf
- **Project ID**: certificate-generator-wolf
- **Configuration**: Already added to the application

## 🚀 Next Steps Required

### 1. Enable Authentication
1. Go to [Firebase Console](https://console.firebase.google.com/project/certificate-generator-wolf)
2. Navigate to "Authentication" → "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Go to "Users" tab → "Add user"
6. Create admin user:
   - **Email**: `<EMAIL>`
   - **Password**: `Tamilselvanadmin6379869678@`

### 2. Enable Firestore Database
1. In Firebase Console, go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select location (closest to your users - recommend `asia-south1` for India)

### 3. ✅ Firebase Configuration (Already Done)
The Firebase configuration is already updated in `src/lib/firebase.ts` with your project details:
- **Project ID**: certificate-generator-wolf
- **Auth Domain**: certificate-generator-wolf.firebaseapp.com
- **API Key**: Configured
- **App ID**: Configured

### 4. Configure Firestore Security Rules
1. In Firestore Database, go to "Rules" tab
2. Replace the default rules with the following **DEVELOPMENT RULES** (for testing):

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // DEVELOPMENT RULES - Allow all operations for testing
    match /certificates/{document} {
      allow read, write: if true;
    }
  }
}
```

3. Click "Publish" to apply the rules

**⚠️ IMPORTANT**: These are development rules for testing. For production, use these secure rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // PRODUCTION RULES - Secure access control
    match /certificates/{document} {
      allow read: if true; // Anyone can verify certificates
      allow write: if request.auth != null &&
                   request.auth.token.email == "<EMAIL>";
    }
  }
}
```

**Setup Steps:**
1. **First**: Use the DEVELOPMENT rules to test the system
2. **After testing**: Switch to PRODUCTION rules for security
3. **Make sure**: Admin user is created in Firebase Authentication

## Features Implemented

### Certificate Creation (Admin Only)
- **Route**: `/certificate-create`
- **Access**: Admin only (<EMAIL>)
- **Password**: Tamilselvanadmin6379869678@
- **Features**:
  - Secure admin authentication
  - 16-digit certificate ID generation (CWT + 13 digits)
  - Complete student and course information storage
  - Firebase Firestore integration

### Certificate Verification (Public)
- **Route**: `/certificate-verify`
- **Access**: Public
- **Features**:
  - Certificate ID lookup
  - Real-time verification
  - Complete certificate details display
  - Secure database queries

## Certificate ID Format
- **Format**: CWT + 13 digits
- **Example**: CWT1234567890123
- **Generation**: Timestamp-based with random component for uniqueness

## Database Structure
```
certificates/
├── certificateId (string) - Unique 16-digit ID
├── name (string) - Student full name
├── age (number) - Student age
├── courseName (string) - Course name (e.g., Web Penetration Testing)
├── college (string) - College/Institution name
├── country (string) - Student country
├── state (string) - Student state
├── courseStartDate (string, optional) - Course start date
├── courseJoiningDate (string) - Course joining date
├── courseEndDate (string) - Course end date
├── certificateProvideDate (string) - Certificate issue date
├── createdAt (timestamp) - Creation timestamp
├── createdBy (string) - "admin"
└── status (string) - "active"
```

## Security Features
1. **Admin Authentication**: Email/password verification
2. **Secure Certificate IDs**: Unique 16-digit generation
3. **Database Security**: Firestore rules for access control
4. **Input Validation**: Zod schema validation
5. **Error Handling**: Comprehensive error management

## Usage Instructions

### For Administrators:
1. Navigate to `/certificate-create`
2. Login with admin credentials
3. Fill in student and course details
4. Generate certificate with unique ID
5. Share certificate ID with student

### For Certificate Verification:
1. Navigate to `/certificate-verify`
2. Enter 16-digit certificate ID
3. View complete certificate details
4. Verify authenticity

## Troubleshooting

### 🚨 **Permission Denied Error (Most Common)**
**Error**: "Permission denied" when creating certificates

**Solutions**:
1. **Use Development Rules First**:
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /certificates/{document} {
         allow read, write: if true;
       }
     }
   }
   ```

2. **Check Firebase Console**:
   - Go to Firestore Database → Rules
   - Make sure rules are published
   - Check for syntax errors in rules

3. **Verify Admin User**:
   - Go to Authentication → Users
   - Ensure `<EMAIL>` exists
   - Password should be `Tamilselvanadmin6379869678@`

4. **Test Authentication**:
   - Try logging in to admin panel
   - Check browser console for auth errors

### 🔧 **Other Common Issues**
1. **Firebase Connection Issues**: Check configuration in `firebase.ts`
2. **Authentication Errors**: Verify admin credentials in Firebase Auth
3. **Database Errors**: Check Firestore rules and permissions
4. **Certificate Not Found**: Verify certificate ID format and existence

### 📝 **Step-by-Step Fix for Permission Error**:
1. Open [Firebase Console](https://console.firebase.google.com/project/certificate-generator-wolf)
2. Go to "Firestore Database"
3. Click "Rules" tab
4. Replace with development rules (shown above)
5. Click "Publish"
6. Test certificate creation
7. If working, switch to production rules later

## Development Notes
- Components use consistent theme with existing website
- Glassmorphism design pattern maintained
- Responsive design for all devices
- Toast notifications for user feedback
- Form validation with Zod
- TypeScript for type safety
