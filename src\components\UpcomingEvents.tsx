import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, Shield, Terminal, Trophy, Code, Network, Server } from 'lucide-react';
import { <PERSON> } from 'react-router-dom';
import { cn } from '@/lib/utils';
import speaker1 from '@/assets/speaker2.png';
import speaker2 from '@/assets/arun.png';

interface EventProps {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  duration: string;
  coordinator: {
    name: string;
    image: string;
  };
  type: 'ctf' | 'class';
  icon: React.ReactNode;
  difficulty: 'Basic' | 'Intermediate' | 'Advanced';
  topics: string[];
}

const events: EventProps[] = [
  {
    id: '1',
    title: 'Sumall CTF Challenges - 24 Hours Challenge',
    description: 'Intensive 24-hour CTF challenge focusing on Basic Web Exploitation and Networking Exploits. Test your skills in real-world scenarios and compete with fellow cybersecurity enthusiasts.',
    date: '23/07/2025 to 24/07/2025',
    time: '10:00',
    duration: '24 Hours',
    coordinator: {
      name: '<PERSON><PERSON>',
      image: speaker1
    },
    type: 'ctf',
    icon: <Trophy className="h-6 w-6" />,
    difficulty: 'Basic',
    topics: ['Web Exploitation', 'Network Exploits', 'CTF Challenges', 'Penetration Testing']
  },
  {
    id: '2',
    title: 'Server Side Hacking Class',
    description: 'Comprehensive server-side hacking class covering basic techniques, vulnerabilities, and exploitation methods. Learn to identify and exploit server-side vulnerabilities in a controlled environment.',
    date: '25/07/2025',
    time: '10:00',
    duration: '3 Hours',
    coordinator: {
      name: 'K.Arun',
      image: speaker2
    },
    type: 'class',
    icon: <Server className="h-6 w-6" />,
    difficulty: 'Basic',
    topics: ['Server Vulnerabilities', 'SQL Injection', 'Command Injection', 'File Upload Attacks']
  }
];

// Auto-typing animation hook
const useTypewriter = (text: string, speed: number = 100) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, speed]);

  return displayText;
};

// Hacking commands for auto-typing
const hackingCommands = [
  "nmap -sS -O target.com",
  "sqlmap -u 'http://target.com/login.php' --dbs",
  "gobuster dir -u http://target.com -w /usr/share/wordlists/common.txt",
  "nikto -h http://target.com",
  "hydra -l admin -P passwords.txt target.com http-post-form"
];

const TerminalHeader = () => {
  const [currentCommandIndex, setCurrentCommandIndex] = useState(0);
  const [showCursor, setShowCursor] = useState(true);

  const currentCommand = useTypewriter(hackingCommands[currentCommandIndex], 80);

  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);

    return () => clearInterval(cursorInterval);
  }, []);

  useEffect(() => {
    if (currentCommand === hackingCommands[currentCommandIndex]) {
      const timeout = setTimeout(() => {
        setCurrentCommandIndex(prev => (prev + 1) % hackingCommands.length);
      }, 2000);

      return () => clearTimeout(timeout);
    }
  }, [currentCommand, currentCommandIndex]);

  return (
    <div className="bg-gradient-to-r from-gray-900 to-gray-800 border border-psyco-green-muted/40 rounded-t-3xl p-6 font-mono text-sm shadow-2xl">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full shadow-lg hover:bg-red-400 transition-colors cursor-pointer"></div>
            <div className="w-4 h-4 bg-yellow-500 rounded-full shadow-lg hover:bg-yellow-400 transition-colors cursor-pointer"></div>
            <div className="w-4 h-4 bg-green-500 rounded-full shadow-lg hover:bg-green-400 transition-colors cursor-pointer"></div>
          </div>
          <div className="text-psyco-green-light flex items-center space-x-2">
            <Terminal className="h-5 w-5" />
            <span className="font-semibold">Wolf@root:~</span>
            <span className="text-white">Cyber Wolf --h</span>
          </div>
        </div>
        <div className="text-xs text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
          Events Terminal v2.0
        </div>
      </div>
      <div className="mt-4 text-psyco-green-light bg-black/30 p-4 rounded-xl border border-gray-700">
        <div className="flex items-center">
          <span className="text-gray-400 mr-2">$</span>
          <span className="text-yellow-400">{currentCommand}</span>
          <span className="ml-2 terminal-cursor">|</span>
        </div>
      </div>
    </div>
  );
};

const EventCard = ({ event }: { event: EventProps }) => {
  const getTypeColor = (type: string) => {
    return type === 'ctf' ? 'text-yellow-400' : 'text-blue-400';
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Basic': return 'text-green-400';
      case 'Intermediate': return 'text-yellow-400';
      case 'Advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="glassmorphism p-0 card-hover animate-fade-in rounded-2xl border-2 border-psyco-green-muted/40 overflow-hidden shadow-2xl hover:shadow-psyco-green-DEFAULT/20 transition-all duration-500 hover:scale-105">
      {/* Modern Terminal-style header for each event */}
      <div className="bg-gradient-to-r from-gray-900 to-gray-800 p-4 font-mono text-sm border-b border-psyco-green-muted/40">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full shadow-md hover:bg-red-400 transition-colors cursor-pointer"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full shadow-md hover:bg-yellow-400 transition-colors cursor-pointer"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full shadow-md hover:bg-green-400 transition-colors cursor-pointer"></div>
            </div>
            <span className="text-psyco-green-light font-semibold">event_{event.id}.exe</span>
          </div>
          <div className="flex items-center space-x-2 bg-gray-800 px-3 py-1 rounded-full">
            <span className="text-gray-300 text-xs">{event.type.toUpperCase()}</span>
          </div>
        </div>
      </div>

      {/* Event Content with Enhanced Padding */}
      <div className="p-8 bg-gradient-to-b from-gray-900/50 to-black/50">
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className={cn(
              "p-4 rounded-2xl border-2 shadow-lg",
              event.type === 'ctf' ? 'bg-yellow-500/20 border-yellow-500/50' : 'bg-blue-500/20 border-blue-500/50'
            )}>
              <div className={getTypeColor(event.type)}>
                {event.icon}
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-2xl font-bold text-white mb-3 font-mono">{event.title}</h3>
              <div className="flex items-center space-x-6 text-sm text-gray-400">
                <div className="flex items-center space-x-2 bg-gray-800 px-3 py-2 rounded-xl">
                  <Calendar className="h-4 w-4" />
                  <span className="font-mono">{event.date}</span>
                </div>
                <div className="flex items-center space-x-2 bg-gray-800 px-3 py-2 rounded-xl">
                  <Clock className="h-4 w-4" />
                  <span className="font-mono">{event.time} ({event.duration})</span>
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">
            <span className={cn(
              "px-4 py-2 rounded-full text-sm font-bold shadow-lg border-2",
              event.type === 'ctf' ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50' : 'bg-blue-500/20 text-blue-400 border-blue-500/50'
            )}>
              {event.type.toUpperCase()}
            </span>
          </div>
        </div>

        <div className="mb-8">
          <p className="text-gray-300 text-base leading-relaxed bg-gray-900/30 p-6 rounded-2xl border border-gray-700">
            {event.description}
          </p>
        </div>

        <div className="mb-8">
          <h4 className="text-lg font-semibold text-psyco-green-light mb-4 font-mono flex items-center">
            <Code className="h-5 w-5 mr-2" />
            Topics Covered:
          </h4>
          <div className="flex flex-wrap gap-3">
            {event.topics.map((topic, index) => (
              <span
                key={index}
                className="px-4 py-2 bg-psyco-green-muted/20 text-psyco-green-light text-sm rounded-xl border border-psyco-green-muted/30 font-mono hover:bg-psyco-green-muted/30 transition-colors duration-200"
              >
                {topic}
              </span>
            ))}
          </div>
        </div>

        <div className="flex items-center justify-between pt-6 border-t border-gray-700">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 rounded-2xl overflow-hidden border-3 border-psyco-green-muted bg-gray-800 shadow-lg">
              <img
                src={event.coordinator.image}
                alt={event.coordinator.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <div className="flex items-center space-x-2 text-sm text-gray-400 font-mono mb-1">
                <User className="h-4 w-4" />
                <span className="bg-gray-800 px-2 py-1 rounded">root@coordinator:</span>
              </div>
              <p className="text-white font-semibold text-lg font-mono">{event.coordinator.name}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-400 mb-2 font-mono">./difficulty --level</div>
            <span className={cn(
              "text-base font-bold font-mono px-4 py-2 border-2 rounded-xl shadow-lg",
              event.difficulty === 'Basic' ? "text-green-400 border-green-400/50 bg-green-400/20" :
              event.difficulty === 'Intermediate' ? "text-yellow-400 border-yellow-400/50 bg-yellow-400/20" :
              "text-red-400 border-red-400/50 bg-red-400/20"
            )}>
              {event.difficulty}
            </span>
          </div>
        </div>

        {/* Enhanced Terminal command simulation */}
        <div className="mt-6 bg-gradient-to-r from-black to-gray-900 p-6 rounded-2xl border-2 border-gray-700 font-mono text-sm shadow-inner">
          <div className="text-psyco-green-light mb-2">
            <span className="text-gray-400">$</span> ./start_event.sh --type={event.type} --date={event.date}
          </div>
          <div className="space-y-1 ml-4">
            <div className="text-gray-300">
              <span className="text-green-400 bg-green-400/20 px-2 py-1 rounded">[INIT]</span> Initializing event system...
            </div>
            <div className="text-gray-300">
              <span className="text-blue-400 bg-blue-400/20 px-2 py-1 rounded">[LOAD]</span> Loading {event.type} framework...
            </div>
            <div className="text-gray-300">
              <span className="text-green-400 bg-green-400/20 px-2 py-1 rounded">[READY]</span> Event initialized successfully
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const UpcomingEvents = () => {
  return (
    <section className="py-16 px-6 md:px-12 bg-gradient-to-b from-psyco-black-DEFAULT to-psyco-black-card">
      <div className="max-w-6xl mx-auto">
        {/* Enhanced Terminal Header with Modern Padding */}
        <div className="mb-12 terminal-glow">
          <TerminalHeader />
          <div className="bg-gradient-to-b from-black to-gray-900 border-x border-b border-psyco-green-muted/40 rounded-b-3xl p-8 font-mono terminal-scan shadow-2xl">
            <div className="space-y-4">
              <div className="text-psyco-green-light flex items-center">
                <span className="text-gray-400 mr-2">$</span>
                <span className="bg-gray-800 px-3 py-1 rounded-lg">./upcoming_events.sh --list</span>
              </div>
              <div className="ml-6 space-y-3">
                <div className="text-gray-300 flex items-center">
                  <span className="text-psyco-green-DEFAULT bg-psyco-green-DEFAULT/20 px-3 py-1 rounded-lg mr-3">[INFO]</span>
                  Loading upcoming cybersecurity events...
                </div>
                <div className="text-gray-300 flex items-center">
                  <span className="text-psyco-green-DEFAULT bg-psyco-green-DEFAULT/20 px-3 py-1 rounded-lg mr-3">[SUCCESS]</span>
                  Found {events.length} upcoming events
                </div>
                <div className="text-gray-300 flex items-center">
                  <span className="text-blue-400 bg-blue-400/20 px-3 py-1 rounded-lg mr-3">[SCAN]</span>
                  Scanning for vulnerabilities in event database...
                </div>
                <div className="text-gray-300 flex items-center">
                  <span className="text-yellow-400 bg-yellow-400/20 px-3 py-1 rounded-lg mr-3">[EXPLOIT]</span>
                  Preparing penetration testing scenarios...
                </div>
                <div className="text-gray-300 flex items-center">
                  <span className="text-red-400 bg-red-400/20 px-3 py-1 rounded-lg mr-3">[PAYLOAD]</span>
                  Initializing CTF challenge frameworks...
                </div>
                <div className="text-gray-300 flex items-center">
                  <span className="text-purple-400 bg-purple-400/20 px-3 py-1 rounded-lg mr-3">[READY]</span>
                  System ready for event execution
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Section Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Shield className="h-8 w-8 text-psyco-green-DEFAULT" />
            <h2 className="text-4xl font-bold text-white">Upcoming Events</h2>
          </div>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Join our upcoming cybersecurity events, CTF challenges, and training sessions. 
            Enhance your skills with hands-on experience and expert guidance.
          </p>
        </div>

        {/* Events Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {events.map((event, index) => (
            <EventCard
              key={event.id}
              event={event}
            />
          ))}
        </div>

        {/* Enhanced Call to Action - Modern Terminal Style */}
        <div className="text-center mt-16">
          <div className="glassmorphism p-0 inline-block rounded-3xl border-2 border-psyco-green-muted/60 overflow-hidden shadow-2xl max-w-2xl">
            {/* Modern Terminal header */}
            <div className="bg-gradient-to-r from-gray-900 to-gray-800 p-6 font-mono text-sm border-b border-psyco-green-muted/40">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full shadow-md hover:bg-red-400 transition-colors cursor-pointer"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full shadow-md hover:bg-yellow-400 transition-colors cursor-pointer"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full shadow-md hover:bg-green-400 transition-colors cursor-pointer"></div>
                  </div>
                  <span className="text-psyco-green-light font-semibold">registration.exe</span>
                </div>
                <div className="text-xs text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
                  Registration Portal
                </div>
              </div>
            </div>

            <div className="p-8 bg-gradient-to-b from-gray-900/50 to-black/50">
              <h3 className="text-3xl font-bold text-white mb-4 font-mono">Ready to Join?</h3>
              <p className="text-gray-400 mb-8 font-mono text-lg leading-relaxed">
                Register now to secure your spot in these exclusive cybersecurity events
              </p>

              {/* Enhanced Terminal command style */}
              <div className="bg-gradient-to-r from-black to-gray-900 p-6 rounded-2xl border-2 border-gray-700 font-mono text-sm mb-8 shadow-inner">
                <div className="space-y-2">
                  <div className="text-psyco-green-light flex items-center">
                    <span className="text-gray-400 mr-2">$</span>
                    <span className="bg-gray-800 px-2 py-1 rounded">./register --events=all --priority=high</span>
                  </div>
                  <div className="ml-4 space-y-1">
                    <div className="text-gray-300">
                      <span className="text-blue-400 bg-blue-400/20 px-2 py-1 rounded">[INIT]</span> Initializing registration system...
                    </div>
                    <div className="text-gray-300">
                      <span className="text-yellow-400 bg-yellow-400/20 px-2 py-1 rounded">[WAITING]</span> Click to execute registration...
                    </div>
                  </div>
                </div>
              </div>

              <Link
                to="/registration"
                className="bg-gradient-to-r from-psyco-green-DEFAULT to-psyco-green-light hover:from-psyco-green-light hover:to-psyco-green-DEFAULT text-white px-8 py-4 rounded-2xl transition-all duration-300 font-bold font-mono inline-block border-2 border-psyco-green-DEFAULT hover:border-psyco-green-light shadow-lg hover:shadow-psyco-green-DEFAULT/50 transform hover:scale-105"
              >
                <div className="flex items-center space-x-2">
                  <Terminal className="h-5 w-5" />
                  <span>EXECUTE REGISTRATION</span>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default UpcomingEvents;
