import React, { useEffect } from "react";
import { Shield, Code, Wifi, Clock, Users, Award, ExternalLink, BookOpen, Target, Zap } from "lucide-react";

const Services = () => {
  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const featuredCourse = {
    id: "cwt-silver",
    title: "CWT Silver Course",
    subtitle: "AI Automation using IoT & Cybersecurity for Smart Home and EV Security",
    description: "Comprehensive course combining AI, IoT, and cybersecurity for modern smart systems and electric vehicle security.",
    duration: "31 Days",
    commitment: "3 to 4 Hours Daily",
    level: "New Course Launch",
    image: "/images/tamil.png",
    modules: [
      "Module 1: Fundamentals of AI, IoT & Cybersecurity",
      "Module 2: IoT-Based Home Automation Robot",
      "Module 3: AI Chatbot Creation",
      "Module 4: Cybersecurity in IoT",
      "Module 5: EV Vehicle AI-Based Security System",
      "Module 6: Project Build & Integration"
    ],
    highlights: [
      "Limited Time Offer",
      "Hands-on Projects",
      "Industry-Relevant Skills",
      "Expert Mentorship"
    ]
  };

  const mainCourses = [
    {
      id: "penetration-testing",
      icon: <Shield size={32} />,
      title: "Penetration Testing",
      level: "Advanced",
      description: "Master the art of ethical hacking and security assessment with comprehensive penetration testing methodologies.",
      duration: "12 Weeks",
      certification: "CEH v12",
      image: "https://cyberwolf-career-guidance.web.app/assets/images/back/cource/pen.png",
      features: [
        "Network Vulnerability Assessment",
        "Web Application Testing",
        "Wireless Network Security",
        "Advanced Exploitation",
        "Report Writing"
      ]
    },
    {
      id: "malware-analysis",
      icon: <Code size={32} />,
      title: "Malware Analysis",
      level: "Expert",
      description: "Deep dive into malware reverse engineering and threat intelligence with advanced analysis techniques.",
      duration: "16 Weeks",
      certification: "GREM",
      image: "https://cyberwolf-career-guidance.web.app/assets/images/back/cource/malwer.png",
      features: [
        "Static & Dynamic Analysis",
        "Assembly Programming",
        "Threat Intelligence",
        "Ransomware Analysis",
        "Reverse Engineering Tools"
      ]
    },
    {
      id: "web-security",
      icon: <Wifi size={32} />,
      title: "Web Security",
      level: "Professional",
      description: "Secure web applications from modern threats with comprehensive security testing and development practices.",
      duration: "14 Weeks",
      certification: "OSWE",
      image: "https://cyberwolf-career-guidance.web.app/assets/images/back/cource/web.png",
      features: [
        "OWASP Top 10 Mastery",
        "Secure Development",
        "Cloud Security",
        "Bug Bounty Training",
        "Authentication Security"
      ]
    }
  ];

  const additionalFeatures = [
    {
      icon: <Target size={24} />,
      title: "Hands-on Learning",
      description: "Practical labs and real-world scenarios for comprehensive skill development"
    },
    {
      icon: <Users size={24} />,
      title: "Expert Mentorship",
      description: "Learn from industry professionals with years of cybersecurity experience"
    },
    {
      icon: <Award size={24} />,
      title: "Industry Certifications",
      description: "Prepare for leading cybersecurity certifications and career advancement"
    },
    {
      icon: <BookOpen size={24} />,
      title: "Comprehensive Resources",
      description: "Access to extensive learning materials, tools, and practice environments"
    },
    {
      icon: <Zap size={24} />,
      title: "Career Support",
      description: "Job placement assistance and career guidance for cybersecurity roles"
    },
    {
      icon: <Clock size={24} />,
      title: "Flexible Learning",
      description: "Self-paced learning with live sessions and recorded content"
    }
  ];



  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="bg-psyco-black-light py-20 px-6 md:px-12 relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2"></div>
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 animate-fade-in">Cybersecurity Courses</h1>
            <p className="text-xl text-gray-300 mb-8 animate-fade-in animation-delay-100">
              Master cybersecurity with our comprehensive courses. From AI automation and IoT security to advanced penetration testing, we provide industry-leading training for the next generation of security professionals.
            </p>
            <a
              href="https://cyberwolf-career-guidance.web.app/details.html"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-3 px-8 rounded-lg transition-all duration-300 btn-glow animate-fade-in animation-delay-200"
            >
              View Course Details
              <ExternalLink className="ml-2 h-5 w-5" />
            </a>
          </div>
        </div>
      </section>

      {/* Featured Course - CWT Silver */}
      <section className="py-20 px-6 md:px-12 bg-gradient-to-r from-psyco-green-DEFAULT/10 to-transparent">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <div className="inline-block bg-psyco-green-DEFAULT text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
              🚀 New Course Launch
            </div>
            <h2 className="text-3xl font-bold text-white mb-4">{featuredCourse.title}</h2>
            <h3 className="text-xl text-psyco-green-DEFAULT mb-6">{featuredCourse.subtitle}</h3>
            <p className="text-gray-300 max-w-3xl mx-auto mb-8">{featuredCourse.description}</p>
          </div>

          <div className="glassmorphism rounded-2xl overflow-hidden">
            <div className="flex flex-col lg:flex-row">
              <div className="lg:w-1/2">
                <img
                  src={featuredCourse.image}
                  alt={featuredCourse.title}
                  className="w-full h-64 lg:h-full object-cover"
                />
              </div>
              <div className="lg:w-1/2 p-8">
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <Clock className="h-6 w-6 text-psyco-green-DEFAULT mx-auto mb-2" />
                    <div className="text-white font-semibold">{featuredCourse.duration}</div>
                    <div className="text-gray-400 text-sm">Duration</div>
                  </div>
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <Users className="h-6 w-6 text-psyco-green-DEFAULT mx-auto mb-2" />
                    <div className="text-white font-semibold">{featuredCourse.commitment}</div>
                    <div className="text-gray-400 text-sm">Daily Commitment</div>
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-white mb-4">Course Modules:</h4>
                  <ul className="space-y-2">
                    {featuredCourse.modules.map((module, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-2 h-2 bg-psyco-green-DEFAULT rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-300 text-sm">{module}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex flex-wrap gap-2 mb-6">
                  {featuredCourse.highlights.map((highlight, index) => (
                    <span key={index} className="bg-psyco-green-DEFAULT/20 text-psyco-green-DEFAULT px-3 py-1 rounded-full text-sm">
                      {highlight}
                    </span>
                  ))}
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <a
                    href="https://cyberwolf-career-guidance.web.app/registration.html"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 text-center"
                  >
                    Enroll Now - Limited Time Offer!
                  </a>
                  <a
                    href="https://cyberwolf-career-guidance.web.app/details.html"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 border border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10 font-medium py-3 px-6 rounded-lg transition-all duration-300 text-center"
                  >
                    Course Details
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Services */}
      <section className="py-20 px-6 md:px-12">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-2">Professional Cybersecurity Courses</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Comprehensive cybersecurity training programs designed for career advancement
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {mainCourses.map((course, index) => (
              <div
                key={course.id}
                className="glassmorphism rounded-2xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300 animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="relative">
                  <img
                    src={course.image}
                    alt={course.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-psyco-green-DEFAULT text-white px-3 py-1 rounded-full text-sm font-medium">
                      {course.level}
                    </span>
                  </div>
                  <div className="absolute top-4 right-4 text-psyco-green-DEFAULT bg-white/10 backdrop-blur-sm rounded-full p-2">
                    {course.icon}
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-2">{course.title}</h3>
                  <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                    {course.description}
                  </p>

                  <div className="flex justify-between items-center mb-4">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-psyco-green-DEFAULT" />
                      <span className="text-gray-300 text-sm">{course.duration}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Award className="h-4 w-4 text-psyco-green-DEFAULT" />
                      <span className="text-gray-300 text-sm">{course.certification}</span>
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-white mb-3">Course Features:</h4>
                    <ul className="space-y-1">
                      {course.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start">
                          <div className="w-1.5 h-1.5 bg-psyco-green-DEFAULT rounded-full mt-2 mr-2 flex-shrink-0"></div>
                          <span className="text-gray-300 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <a
                      href="https://cyberwolf-career-guidance.web.app/registration.html"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 text-center text-sm"
                    >
                      Enroll Now
                    </a>
                    <a
                      href="https://cyberwolf-career-guidance.web.app/details.html"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full border border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10 font-medium py-2 px-4 rounded-lg transition-all duration-300 text-center text-sm flex items-center justify-center"
                    >
                      Course Details
                      <ExternalLink className="ml-1 h-3 w-3" />
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Additional Features */}
      <section className="py-20 px-6 md:px-12 bg-psyco-black-light">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-2">Why Choose Our Courses</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Comprehensive features and support to accelerate your cybersecurity career
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {additionalFeatures.map((feature, index) => (
              <div
                key={index}
                className="glassmorphism p-6 card-hover animate-fade-in"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className="text-psyco-green-DEFAULT mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-medium text-white mb-2">{feature.title}</h3>
                <p className="text-gray-300">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-20 px-6 md:px-12 relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute w-96 h-96 bg-psyco-green-DEFAULT/10 rounded-full blur-3xl top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="glassmorphism p-8 md:p-12 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Ready to Start Your Cybersecurity Journey?</h2>
            <p className="text-gray-300 max-w-2xl mx-auto mb-8">
              Join thousands of professionals who have advanced their careers with our comprehensive cybersecurity training programs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://cyberwolf-career-guidance.web.app/registration.html"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white font-medium py-3 px-8 rounded-lg transition-all duration-300 flex items-center justify-center btn-glow"
              >
                Enroll Now
                <ExternalLink className="ml-2 h-5 w-5" />
              </a>
              <a
                href="https://cyberwolf-career-guidance.web.app/details.html"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-transparent border border-psyco-green-DEFAULT text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10 font-medium py-3 px-8 rounded-lg transition-all duration-300 flex items-center justify-center"
              >
                View Course Details
                <ExternalLink className="ml-2 h-5 w-5" />
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;
