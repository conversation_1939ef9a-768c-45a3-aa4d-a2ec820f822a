@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 7%;
    --foreground: 0 0% 98%;

    --card: 0 0% 12%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 12%;
    --popover-foreground: 0 0% 98%;

    --primary: 160 84% 39%;
    --primary-foreground: 0 0% 98%;

    --secondary: 160 84% 39%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 16%;
    --muted-foreground: 0 0% 70%;

    --accent: 160 84% 39%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 160 84% 39%;

    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased font-body;
    font-feature-settings: "kern", "liga", "clig", "calt";
    font-size: 14px;
    line-height: 1.6;
    font-weight: 400;
  }

  html {
    @apply scroll-smooth font-sans;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 14px;
    text-rendering: optimizeLegibility;
  }
}

@layer components {
  .glassmorphism {
    @apply backdrop-blur-lg bg-black/70 border border-green-500/10 rounded-2xl;
    box-shadow: 0 4px 30px rgba(16, 185, 129, 0.1);
  }
  
  .card-hover {
    @apply transition-all duration-300 hover:translate-y-[-5px] hover:shadow-lg;
    box-shadow: 0 4px 10px rgba(16, 185, 129, 0);
  }
  
  .card-hover:hover {
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.2);
  }
  
  .btn-glow {
    @apply relative overflow-hidden;
  }
  
  .btn-glow::after {
    @apply content-[''] absolute top-0 left-[-100%] z-0 w-[200%] h-full bg-gradient-to-r from-transparent via-white/20 to-transparent transition-all duration-500;
  }
  
  .btn-glow:hover::after {
    @apply left-[100%];
  }
  
  .text-glow {
    text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  }
  
  .section-padding {
    @apply py-20 px-6 md:px-12 lg:px-24;
  }
  
  .link-hover {
    position: relative;
  }
  
  .link-hover::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    width: 0;
    background-color: #10B981;
    transition: all 0.3s;
  }
  
  .link-hover:hover::after {
    width: 100%;
  }

  /* Enhanced navbar styles */
  .navbar-enhanced {
    @apply backdrop-blur-xl bg-black/90 border-b border-green-500/20;
    box-shadow: 0 4px 30px rgba(16, 185, 129, 0.1);
  }

  .nav-link-enhanced {
    @apply relative overflow-hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .nav-link-enhanced::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-r from-green-500/0 via-green-500/10 to-green-500/0;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .nav-link-enhanced:hover::before {
    transform: translateX(100%);
  }

  /* Touch-friendly improvements */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile menu animations */
  .mobile-menu-enter {
    @apply opacity-0 translate-y-[-10px] scale-95;
  }

  .mobile-menu-enter-active {
    @apply opacity-100 translate-y-0 scale-100;
    transition: all 0.2s ease-out;
  }

  .mobile-menu-exit {
    @apply opacity-100 translate-y-0 scale-100;
  }

  .mobile-menu-exit-active {
    @apply opacity-0 translate-y-[-10px] scale-95;
    transition: all 0.15s ease-in;
  }

  /* Responsive font sizes */
  @media (max-width: 640px) {
    html {
      font-size: 13px;
    }
    
    .nav-text-mobile {
      font-size: 0.875rem;
    }
  }

  @media (min-width: 641px) and (max-width: 768px) {
    html {
      font-size: 14px;
    }
  }

  @media (min-width: 769px) {
    html {
      font-size: 15px;
    }
  }

  /* Improved touch scrolling */
  .scroll-smooth-touch {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Focus improvements for accessibility */
  .focus-enhanced:focus {
    @apply outline-none ring-2 ring-green-500/50 ring-offset-2 ring-offset-black;
  }

  /* Enhanced glassmorphism for navbar */
  .glassmorphism-navbar {
    @apply backdrop-blur-xl bg-black/85 border border-green-500/10;
    box-shadow: 
      0 4px 30px rgba(16, 185, 129, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Prevent text selection on touch devices for better UX */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Smooth transitions for all interactive elements */
  .smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Enhanced button styles for touch devices */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
    touch-action: manipulation;
  }

  /* Prevent zoom on double tap for iOS */
  .no-zoom {
    touch-action: manipulation;
  }

  /* Better scrollbar for mobile */
  @media (max-width: 768px) {
    ::-webkit-scrollbar {
      width: 4px;
    }
    
    ::-webkit-scrollbar-thumb {
      @apply bg-green-600 rounded-full;
    }
  }

  /* Fix for iOS Safari viewport height issues */
  @supports (-webkit-touch-callout: none) {
    .min-h-screen {
      min-height: -webkit-fill-available;
    }
  }

  /* Improved focus states for keyboard navigation */
  .focus-visible:focus-visible {
    @apply outline-none ring-2 ring-green-500/70 ring-offset-2 ring-offset-black;
  }

  /* Animation for mobile menu items */
  @keyframes slideInFromRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .mobile-menu-item {
    animation: slideInFromRight 0.3s ease-out forwards;
  }

  .mobile-menu-item:nth-child(1) { animation-delay: 0.1s; }
  .mobile-menu-item:nth-child(2) { animation-delay: 0.15s; }
  .mobile-menu-item:nth-child(3) { animation-delay: 0.2s; }
  .mobile-menu-item:nth-child(4) { animation-delay: 0.25s; }
  .mobile-menu-item:nth-child(5) { animation-delay: 0.3s; }

  /* Terminal animations */
  @keyframes terminalBlink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }

  @keyframes terminalGlow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(16, 185, 129, 0.3);
    }
    50% {
      box-shadow: 0 0 20px rgba(16, 185, 129, 0.6), 0 0 30px rgba(16, 185, 129, 0.4);
    }
  }

  @keyframes scanLine {
    0% { transform: translateY(-100%); }
    100% { transform: translateY(100vh); }
  }

  .terminal-cursor {
    animation: terminalBlink 0.8s infinite;
    will-change: opacity;
  }

  .terminal-glow {
    animation: terminalGlow 2s ease-in-out infinite;
  }

  .terminal-scan {
    position: relative;
    overflow: hidden;
  }

  .terminal-scan::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.8), transparent);
    animation: scanLine 3s linear infinite;
    z-index: 1;
  }

  /* Enhanced terminal effects */
  .terminal-window {
    background: linear-gradient(135deg, #1a1a1a 0%, #0d1117 100%);
    box-shadow:
      0 0 20px rgba(16, 185, 129, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .terminal-header {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    border-bottom: 1px solid rgba(16, 185, 129, 0.2);
  }

  .command-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .command-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.3),
      0 0 30px rgba(16, 185, 129, 0.2);
  }

  .typing-effect {
    overflow: hidden;
    white-space: nowrap;
    animation: typing 2s steps(40, end);
  }

  @keyframes typing {
    from { width: 0; }
    to { width: 100%; }
  }

  .terminal-prompt::before {
    content: '$ ';
    color: #6b7280;
    margin-right: 0.5rem;
  }

  /* Performance optimizations for References page */
  .references-container {
    contain: layout style paint;
    will-change: transform;
  }

  .attack-card {
    contain: layout style;
    transform: translateZ(0);
  }

  .code-block {
    contain: layout style;
    overflow: hidden;
  }

  .code-block pre {
    contain: layout;
    white-space: pre-wrap;
    word-break: break-word;
  }

  /* Optimize animations */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .animate-fade-in,
    .card-hover,
    .terminal-glow,
    .terminal-scan {
      animation: none !important;
      transition: none !important;
    }
  }
}

.calendar-container .react-calendar {
  @apply glassmorphism border-green-500/10 p-4;
}

.calendar-container .react-calendar__tile--active {
  @apply bg-green-500 text-white;
}

.calendar-container .react-calendar__tile--now {
  @apply bg-green-500/10 text-green-500;
}

.calendar-container .react-calendar__navigation button:enabled:hover,
.calendar-container .react-calendar__navigation button:enabled:focus,
.calendar-container .react-calendar__tile:enabled:hover,
.calendar-container .react-calendar__tile:enabled:focus {
  @apply bg-green-500/10 text-white;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-green-700 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-green-500;
}

/* Enhanced scrollbar for popup */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #10b981 #1f2937;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: #1f2937;
  border-radius: 9999px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #10b981;
  border-radius: 9999px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #059669;
}

/* Smooth scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
}
