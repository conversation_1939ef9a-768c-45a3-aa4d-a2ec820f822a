import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Search, Award, User, Calendar, MapPin, GraduationCap, CheckCircle, XCircle, Shield, Clock, Copy, Check } from 'lucide-react';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface CertificateData {
  certificateId: string;
  name: string;
  age: number;
  courseName: string;
  college: string;
  country: string;
  state: string;
  courseStartDate?: string;
  courseJoiningDate: string;
  courseEndDate: string;
  certificateProvideDate: string;
  createdAt: any;
  status: string;
}

const CertificateIdVerifications = () => {
  const [certificateId, setCertificateId] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [certificateData, setCertificateData] = useState<CertificateData | null>(null);
  const [searchAttempted, setSearchAttempted] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const { toast } = useToast();

  // Search for certificate
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!certificateId.trim()) {
      toast({
        title: "Invalid Input",
        description: "Please enter a certificate ID",
        variant: "destructive",
      });
      return;
    }

    setIsSearching(true);
    setSearchAttempted(true);
    setCertificateData(null);

    try {
      if (!db) {
        throw new Error("Firebase not configured. Please check FIREBASE_SETUP.md for setup instructions.");
      }

      const q = query(
        collection(db, 'certificates'),
        where('certificateId', '==', certificateId.trim().toUpperCase())
      );

      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0];
        const data = doc.data() as CertificateData;
        setCertificateData(data);

        toast({
          title: "Certificate Found!",
          description: `Valid certificate for ${data.name}`,
        });
      } else {
        setCertificateData(null);
        toast({
          title: "Certificate Not Found",
          description: "No certificate found with this ID. Please check and try again.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error searching certificate:", error);
      toast({
        title: "Search Error",
        description: error.message || "Failed to search certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Clear search
  const handleClear = () => {
    setCertificateId("");
    setCertificateData(null);
    setSearchAttempted(false);
    setIsCopied(false);
  };

  // Copy certificate ID to clipboard
  const copyCertificateId = async (certificateId: string) => {
    try {
      await navigator.clipboard.writeText(certificateId);
      setIsCopied(true);
      toast({
        title: "Copied!",
        description: "Certificate ID copied to clipboard",
      });

      // Reset copy status after 2 seconds
      setTimeout(() => {
        setIsCopied(false);
      }, 2000);
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy certificate ID. Please copy manually.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-psyco-black-DEFAULT via-psyco-black-light to-psyco-black-DEFAULT p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-psyco-green-DEFAULT rounded-full flex items-center justify-center mb-4">
            <Shield className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-white mb-2">Certificate Verification</h1>
          <p className="text-gray-400 text-lg">
            Verify the authenticity of Cyber Wolf Training certificates
          </p>
        </div>

        {/* Search Form */}
        <Card className="glassmorphism mb-8">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold text-white flex items-center justify-center gap-2">
              <Search className="h-6 w-6" />
              Certificate Lookup
            </CardTitle>
            <CardDescription className="text-gray-300">
              Enter the 16-digit certificate ID to verify its authenticity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="certificateId" className="text-white flex items-center gap-2">
                  <Award size={16} />
                  Certificate ID
                </Label>
                <div className="relative">
                  <Input
                    id="certificateId"
                    type="text"
                    value={certificateId}
                    onChange={(e) => setCertificateId(e.target.value.toUpperCase())}
                    placeholder="Enter certificate ID (e.g., CWT1234567890123)"
                    className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 text-center text-lg font-mono pr-12"
                    maxLength={16}
                  />
                  {certificateId && (
                    <Button
                      type="button"
                      onClick={() => copyCertificateId(certificateId)}
                      variant="ghost"
                      size="sm"
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 text-gray-400 hover:text-psyco-green-DEFAULT"
                    >
                      {isCopied ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  )}
                </div>
                <p className="text-gray-400 text-sm text-center">
                  Certificate ID format: CWT followed by 13 digits
                </p>
              </div>
              
              <div className="flex gap-4">
                <Button
                  type="submit"
                  className="flex-1 bg-psyco-green-DEFAULT hover:bg-psyco-green-dark text-white py-3"
                  disabled={isSearching}
                >
                  {isSearching ? (
                    <>
                      <Search className="mr-2 h-4 w-4 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    <>
                      <Search className="mr-2 h-4 w-4" />
                      Verify Certificate
                    </>
                  )}
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClear}
                  className="border-white/20 text-white hover:bg-white/10"
                >
                  Clear
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchAttempted && (
          <Card className={`glassmorphism ${certificateData ? 'border-psyco-green-DEFAULT/50' : 'border-red-500/50'}`}>
            <CardHeader>
              <CardTitle className={`text-2xl font-bold flex items-center gap-2 ${
                certificateData ? 'text-psyco-green-DEFAULT' : 'text-red-400'
              }`}>
                {certificateData ? (
                  <>
                    <CheckCircle className="h-6 w-6" />
                    Certificate Verified
                  </>
                ) : (
                  <>
                    <XCircle className="h-6 w-6" />
                    Certificate Not Found
                  </>
                )}
              </CardTitle>
              <CardDescription className="text-gray-300">
                {certificateData 
                  ? "This certificate is authentic and valid"
                  : "The certificate ID you entered could not be found in our database"
                }
              </CardDescription>
            </CardHeader>
            
            {certificateData && (
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Personal Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white border-b border-psyco-green-DEFAULT/30 pb-2">
                      Personal Information
                    </h3>
                    
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <User className="h-5 w-5 text-psyco-green-DEFAULT" />
                        <div>
                          <p className="text-gray-400 text-sm">Full Name</p>
                          <p className="text-white font-medium">{certificateData.name}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <Calendar className="h-5 w-5 text-psyco-green-DEFAULT" />
                        <div>
                          <p className="text-gray-400 text-sm">Age</p>
                          <p className="text-white font-medium">{certificateData.age} years</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <GraduationCap className="h-5 w-5 text-psyco-green-DEFAULT" />
                        <div>
                          <p className="text-gray-400 text-sm">Course Name</p>
                          <p className="text-white font-medium">{certificateData.courseName}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <User className="h-5 w-5 text-psyco-green-DEFAULT" />
                        <div>
                          <p className="text-gray-400 text-sm">College/Institution</p>
                          <p className="text-white font-medium">{certificateData.college}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <MapPin className="h-5 w-5 text-psyco-green-DEFAULT" />
                        <div>
                          <p className="text-gray-400 text-sm">Location</p>
                          <p className="text-white font-medium">{certificateData.state}, {certificateData.country}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Course Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white border-b border-psyco-green-DEFAULT/30 pb-2">
                      Course Information
                    </h3>
                    
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Award className="h-5 w-5 text-psyco-green-DEFAULT" />
                          <div>
                            <p className="text-gray-400 text-sm">Certificate ID</p>
                            <p className="text-white font-mono font-medium">{certificateData.certificateId}</p>
                          </div>
                        </div>
                        <Button
                          onClick={() => copyCertificateId(certificateData.certificateId)}
                          variant="outline"
                          size="sm"
                          className="border-psyco-green-DEFAULT/50 text-psyco-green-DEFAULT hover:bg-psyco-green-DEFAULT/10"
                        >
                          {isCopied ? (
                            <>
                              <Check className="mr-1 h-3 w-3" />
                              Copied
                            </>
                          ) : (
                            <>
                              <Copy className="mr-1 h-3 w-3" />
                              Copy
                            </>
                          )}
                        </Button>
                      </div>
                      
                      {certificateData.courseStartDate && (
                        <div className="flex items-center space-x-3">
                          <Calendar className="h-5 w-5 text-psyco-green-DEFAULT" />
                          <div>
                            <p className="text-gray-400 text-sm">Course Start Date</p>
                            <p className="text-white font-medium">{formatDate(certificateData.courseStartDate)}</p>
                          </div>
                        </div>
                      )}
                      
                      <div className="flex items-center space-x-3">
                        <Calendar className="h-5 w-5 text-psyco-green-DEFAULT" />
                        <div>
                          <p className="text-gray-400 text-sm">Course Joining Date</p>
                          <p className="text-white font-medium">{formatDate(certificateData.courseJoiningDate)}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <Calendar className="h-5 w-5 text-psyco-green-DEFAULT" />
                        <div>
                          <p className="text-gray-400 text-sm">Course End Date</p>
                          <p className="text-white font-medium">{formatDate(certificateData.courseEndDate)}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <Award className="h-5 w-5 text-psyco-green-DEFAULT" />
                        <div>
                          <p className="text-gray-400 text-sm">Certificate Issued</p>
                          <p className="text-white font-medium">{formatDate(certificateData.certificateProvideDate)}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Certificate Status */}
                <div className="mt-6 p-4 bg-psyco-green-DEFAULT/10 rounded-lg border border-psyco-green-DEFAULT/30">
                  <div className="flex items-center justify-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-psyco-green-DEFAULT" />
                    <span className="text-psyco-green-DEFAULT font-medium">
                      This certificate is authentic and issued by Cyber Wolf Training
                    </span>
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        )}

        {/* Information Section */}
        <Card className="glassmorphism mt-8">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-white flex items-center gap-2">
              <Shield className="h-5 w-5" />
              About Certificate Verification
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-300">
              <div>
                <h4 className="font-semibold text-white mb-2">How to Verify</h4>
                <ul className="space-y-1 text-sm">
                  <li>• Enter the 16-digit certificate ID</li>
                  <li>• Certificate ID starts with "CWT"</li>
                  <li>• Verification is instant and secure</li>
                  <li>• All data is encrypted and protected</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-white mb-2">Certificate Features</h4>
                <ul className="space-y-1 text-sm">
                  <li>• Unique 16-digit identification</li>
                  <li>• Tamper-proof digital verification</li>
                  <li>• Real-time database validation</li>
                  <li>• Comprehensive course details</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CertificateIdVerifications;
